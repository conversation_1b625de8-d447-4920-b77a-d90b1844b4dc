package cn.bztmaster.cnt.module.talentpool.dal.mysql.talent;

import cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentUserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserPageReqDTO;
import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

/**
 * 人才用户 Mapper
 *
 * 提供人才库用户的数据库操作方法。
 *
 * <AUTHOR>
 */
@Mapper
public interface TalentUserMapper extends BaseMapperX<TalentUserDO> {

        /**
         * 根据用户ID查询人才用户
         * 
         * @param userId 用户ID
         * @return 人才用户DO
         */
        TalentUserDO selectById(@Param("userId") Long userId);

        /**
         * 分页查询人才用户列表
         * 
         * @param keyword 关键字
         * @param source  用户来源
         * @param tag     用户标签
         * @param status  用户状态
         * @param offset  偏移量
         * @param limit   每页条数
         * @return 人才用户DO列表
         */
        List<TalentUserDO> selectPage(@Param("keyword") String keyword,
                        @Param("source") String source,
                        @Param("tag") String tag,
                        @Param("status") String status,
                        @Param("offset") Integer offset,
                        @Param("limit") Integer limit);

        /**
         * 统计分页总数
         */
        int countPage(@Param("keyword") String keyword,
                        @Param("source") String source,
                        @Param("tag") String tag,
                        @Param("status") String status);

        /**
         * 新增人才用户
         * 
         * @param user 人才用户DO
         * @return 影响行数
         */
        int insert(TalentUserDO user);

        /**
         * 更新人才用户
         * 
         * @param user 人才用户DO
         * @return 影响行数
         */
        int update(TalentUserDO user);

        /**
         * 停用人才用户（将状态设为已禁用）
         * 
         * @param userId 用户ID
         * @return 影响行数
         */
        int disable(@Param("userId") Long userId);

        /**
         * 根据身份证号查找用户
         * 
         * @param identityId 身份证号
         * @return 人才用户DO
         */
        TalentUserDO selectByIdentityId(@Param("identityId") String identityId);

        /**
         * 根据手机号查找用户
         * 
         * @param phone 手机号
         * @return 人才用户DO
         */
        TalentUserDO selectByPhone(@Param("phone") String phone);

        /**
         * 根据OneID查找用户
         * 
         * @param oneid OneID
         * @return 人才用户DO
         */
        TalentUserDO selectByOneid(@Param("oneid") String oneid);

        /**
         * 分页查询人才用户列表（推荐方式）
         */
        default PageResult<TalentUserDO> selectPage(cn.bztmaster.cnt.framework.common.pojo.PageParam pageParam) {
                return selectPage(pageParam, new LambdaQueryWrapperX<TalentUserDO>()
                                .orderByDesc(TalentUserDO::getUserId));
        }

        /**
         * 对标 system/user/page，支持多条件分页查询
         */
        default PageResult<TalentUserDO> selectPage(TalentUserPageReqDTO reqDTO) {
                LambdaQueryWrapper<TalentUserDO> wrapper = new LambdaQueryWrapper<TalentUserDO>()
                                .and(reqDTO.getKeyword() != null && !reqDTO.getKeyword().isEmpty(), w -> {
                                        w.like(TalentUserDO::getName, reqDTO.getKeyword())
                                                        .or()
                                                        .like(TalentUserDO::getPhone, reqDTO.getKeyword())
                                                        .or()
                                                        .like(TalentUserDO::getIdentityId, reqDTO.getKeyword());
                                })
                                .apply(reqDTO.getTag() != null && !reqDTO.getTag().isEmpty(),
                                                "user_id IN (SELECT user_id FROM talent_user_tag ut JOIN talent_tag_library tl ON ut.tag_id = tl.tag_id WHERE ut.deleted = 0 AND tl.tag_name LIKE CONCAT('%', {0}, '%'))",
                                                reqDTO.getTag())
                                .orderByDesc(TalentUserDO::getUserId);
                if (reqDTO.getSource() != null && !reqDTO.getSource().isEmpty()) {
                        wrapper.eq(TalentUserDO::getRegisterSource, reqDTO.getSource());
                }
                if (reqDTO.getStatus() != null && !reqDTO.getStatus().isEmpty()) {
                        wrapper.eq(TalentUserDO::getStatus, reqDTO.getStatus());
                }
                return selectPage(reqDTO, wrapper);
        }
}