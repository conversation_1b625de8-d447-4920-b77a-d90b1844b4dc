package cn.bztmaster.cnt.module.publicbiz.service.business.impl;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.business.BusinessFollowupDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.business.BusinessFollowupMapper;
import cn.bztmaster.cnt.module.publicbiz.service.business.BusinessFollowupService;
import cn.bztmaster.cnt.module.publicbiz.service.business.BusinessLogService;
import cn.bztmaster.cnt.module.publicbiz.convert.business.BusinessFollowupConvert;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Collection;
import java.util.Collections;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;

@Service
public class BusinessFollowupServiceImpl implements BusinessFollowupService {
    @Resource
    private BusinessFollowupMapper businessFollowupMapper;
    @Resource
    private BusinessLogService businessLogService;
    @Resource
    private BusinessFollowupConvert businessFollowupConvert;

    @Override
    public PageResult<BusinessFollowupRespVO> getBusinessFollowupPage(BusinessFollowupPageReqVO reqVO) {
        PageResult<BusinessFollowupDO> pageResult = businessFollowupMapper.selectPage(reqVO);
        return businessFollowupConvert.convertPage(pageResult);
    }

    @Override
    @Transactional
    public Long createBusinessFollowup(BusinessFollowupSaveReqVO reqVO) {
        BusinessFollowupDO followup = businessFollowupConvert.convert(reqVO);
        followup.setCreateTime(new Date());
        followup.setDeleted(false);
        // 自动赋默认值
        followup.setFollowTime(new Date());
        followup.setFollowUserId(SecurityFrameworkUtils.getLoginUserId());
        followup.setFollowUserName(SecurityFrameworkUtils.getLoginUserNickname());
        businessFollowupMapper.insert(followup);
        // 日志
        BusinessLogSaveReqVO logReqVO = new BusinessLogSaveReqVO();
        logReqVO.setBusinessId(reqVO.getBusinessId());
        logReqVO.setAction("跟进");
        logReqVO.setContent("商机跟进：" + reqVO.getContent());
        logReqVO.setActionTime(new Date());
        logReqVO.setActionUserId(followup.getFollowUserId());
        logReqVO.setActionUserName(followup.getFollowUserName());
        logReqVO.setCreator(followup.getCreator());
        logReqVO.setDeleted(false);
        businessLogService.createBusinessLog(logReqVO);
        return followup.getId();
    }

    @Override
    public BusinessFollowupRespVO getBusinessFollowupDetail(Long id) {
        BusinessFollowupDO followup = businessFollowupMapper.selectById(id);
        if (followup == null || Boolean.TRUE.equals(followup.getDeleted())) {
            return null;
        }
        return businessFollowupConvert.convert(followup);
    }

    @Override
    public BusinessFollowupDO getBusinessFollowup(Long id) {
        return businessFollowupMapper.selectById(id);
    }

    @Override
    public List<BusinessFollowupDO> getBusinessFollowupList(Collection<Long> ids) {
        return (ids == null || ids.isEmpty()) ? Collections.emptyList() : businessFollowupMapper.selectBatchIds(ids);
    }
} 