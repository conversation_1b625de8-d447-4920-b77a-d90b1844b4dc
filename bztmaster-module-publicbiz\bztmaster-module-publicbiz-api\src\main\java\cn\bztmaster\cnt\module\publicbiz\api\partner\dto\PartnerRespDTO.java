package cn.bztmaster.cnt.module.publicbiz.api.partner.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@Schema(description = "合作伙伴 - 详情 Response DTO")
public class PartnerRespDTO {
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "机构名称")
    private String name;

    @Schema(description = "机构类型")
    private String type;

    @Schema(description = "业务模块")
    private String biz;

    @Schema(description = "合作状态")
    private String status;

    @Schema(description = "风险等级")
    private String risk;

    @Schema(description = "我方负责人ID")
    private Long owner;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
}
