<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.bztmaster.cnt</groupId>
        <artifactId>bztmaster</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>bztmaster-module-talentpool</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>bztmaster-module-talentpool-api</module>
        <module>bztmaster-module-talentpool-server</module>
    </modules>

    <name>${project.artifactId}</name>
    <description>
        talentpool 模块，我们放人才池业务，提供业务的人才池能力。
        例如说：商户、应用、人才、入库等
    </description>


</project>
