package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("publicbiz_service_package")
@Schema(description = "服务套餐主表 DO")
public class ServicePackageDO {
    @TableId
    private Long id;
    private String name;
    private String category;
    private String thumbnail;
    private BigDecimal price;
    private BigDecimal originalPrice;
    private String unit;
    private String serviceDuration;
    private String packageType;
    private String taskSplitRule;
    private String serviceDescription;
    private String serviceDetails;
    private String serviceProcess;
    private String purchaseNotice;
    private String status;
    private Integer advanceBookingDays;
    private String timeSelectionMode;
    private String appointmentMode;
    private String serviceStartTime;
    private String addressSetting;
    private Integer maxBookingDays;
    private String cancellationPolicy;
    private Date createTime;
    private Date updateTime;
    private String creator;
    private String updater;
    private Boolean deleted;
    private Long tenantId;
} 