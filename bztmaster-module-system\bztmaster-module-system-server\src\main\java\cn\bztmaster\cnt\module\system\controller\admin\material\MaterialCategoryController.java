package cn.bztmaster.cnt.module.system.controller.admin.material;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.service.material.MaterialCategoryService;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/system/material/category")
@Tag(name = "素材库-分类管理")
@Slf4j
@Validated
public class MaterialCategoryController {
    @Resource
    private MaterialCategoryService categoryService;

    @GetMapping("/list")
    @Operation(summary = "分类列表")
    public CommonResult<List<CategoryListRespVO>> list() {
        return CommonResult.success(categoryService.getCategoryList());
    }

    @PostMapping("/create")
    @Operation(summary = "新增分类")
    public CommonResult<Long> create(@RequestBody @Validated CategoryCreateReqVO reqVO) {
        return CommonResult.success(categoryService.createCategory(reqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "编辑分类")
    public CommonResult<Boolean> update(@RequestBody @Validated CategoryUpdateReqVO reqVO) {
        categoryService.updateCategory(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除分类")
    public CommonResult<Boolean> delete(@RequestBody @Validated CategoryDeleteReqVO reqVO) {
        categoryService.deleteCategory(reqVO.getId());
        return CommonResult.success(true);
    }
} 