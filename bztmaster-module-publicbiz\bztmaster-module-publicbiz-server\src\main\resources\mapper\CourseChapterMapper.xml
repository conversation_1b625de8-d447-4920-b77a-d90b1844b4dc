<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.digitalAsset.CourseChapterMapper">
    
    <resultMap id="BaseResultMap" type="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset.CourseChapterDO">
        <id property="id" column="id" />
        <result property="courseId" column="course_id" />
        <result property="title" column="title" />
        <result property="sortOrder" column="sort_order" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="creator" column="creator" />
        <result property="updater" column="updater" />
        <result property="deleted" column="deleted" />
        <result property="tenantId" column="tenant_id" />
    </resultMap>

    <!-- 根据课程ID查询章节列表 -->
    <select id="selectListByCourseId" resultMap="BaseResultMap">
        SELECT id, course_id, title, sort_order, create_time, update_time, 
               creator, updater, deleted, tenant_id
        FROM publicbiz_course_chapter
        WHERE deleted = 0 AND course_id = #{courseId}
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 根据课程ID和章节标题查询（用于重复性校验） -->
    <select id="selectByCourseIdAndTitle" resultMap="BaseResultMap">
        SELECT id, course_id, title, sort_order, create_time, update_time, 
               creator, updater, deleted, tenant_id
        FROM publicbiz_course_chapter
        WHERE deleted = 0 AND course_id = #{courseId} AND title = #{title}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
        LIMIT 1
    </select>

    <!-- 根据课程ID统计章节数量 -->
    <select id="countByCourseId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM publicbiz_course_chapter
        WHERE deleted = 0 AND course_id = #{courseId}
    </select>

    <!-- 获取课程下一个排序序号 -->
    <select id="getNextSortOrder" resultType="java.lang.Integer">
        SELECT IFNULL(MAX(sort_order), 0) + 1
        FROM publicbiz_course_chapter
        WHERE deleted = 0 AND course_id = #{courseId}
    </select>

    <!-- 批量删除课程下的所有章节 -->
    <update id="deleteByCourseId">
        UPDATE publicbiz_course_chapter 
        SET deleted = 1, update_time = NOW()
        WHERE course_id = #{courseId} AND deleted = 0
    </update>

</mapper>
