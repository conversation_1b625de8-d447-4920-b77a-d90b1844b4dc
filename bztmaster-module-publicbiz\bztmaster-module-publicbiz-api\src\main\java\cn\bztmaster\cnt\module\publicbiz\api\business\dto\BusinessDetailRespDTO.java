package cn.bztmaster.cnt.module.publicbiz.api.business.dto;

import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Data
@Schema(description = "商机中心 - 商机详情 Response DTO")
public class BusinessDetailRespDTO implements VO {
    @Schema(description = "商机基本信息")
    private BusinessRespDTO business;

    @Schema(description = "商机跟进记录列表")
    private List<BusinessFollowupRespDTO> followups;
} 