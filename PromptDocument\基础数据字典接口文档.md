# 基础数据字典接口文档

---

## 1. 字典数据分页查询

- **接口地址**：`GET /system/dict-data/page`
- **功能说明**：分页查询字典数据列表，可按字典类型、标签、状态等条件筛选。

### 请求方式

`GET`

### 请求参数

| 参数名    | 类型    | 必填 | 说明                         |
|-----------|---------|------|------------------------------|
| label     | string  | 否   | 字典标签，模糊查询           |
| dictType  | string  | 否   | 字典类型，模糊查询           |
| status    | int     | 否   | 展示状态（0-禁用，1-启用）   |
| pageNo    | int     | 否   | 页码，默认1                  |
| pageSize  | int     | 否   | 每页条数，默认10             |

> 说明：分页参数 `pageNo`、`pageSize` 继承自通用分页参数。

### 返回字段

| 字段名        | 类型           | 说明                   |
|---------------|----------------|------------------------|
| total         | integer        | 总记录数               |
| list          | array          | 字典数据列表           |
| └─ id         | long           | 字典数据编号           |
| └─ sort       | integer        | 显示顺序               |
| └─ label      | string         | 字典标签               |
| └─ value      | string         | 字典值                 |
| └─ dictType   | string         | 字典类型               |
| └─ status     | integer        | 状态（0-禁用，1-启用） |
| └─ colorType  | string         | 颜色类型（如default、primary等）|
| └─ cssClass   | string         | CSS样式类名            |
| └─ remark     | string         | 备注                   |
| └─ createTime | string         | 创建时间（时间戳格式） |

### 请求示例

```http
GET /system/dict-data/page?dictType=talent_sources&label=&pageNo=1&pageSize=10&status=0
```

### 返回示例

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 3007,
        "sort": 5,
        "label": "家政服务员注册",
        "value": "家政服务员注册",
        "dictType": "talent_sources",
        "status": 0,
        "colorType": "default",
        "cssClass": "",
        "remark": "",
        "createTime": 1752549584000
      },
      {
        "id": 3006,
        "sort": 4,
        "label": "高校实践小程序",
        "value": "高校实践小程序",
        "dictType": "talent_sources",
        "status": 0,
        "colorType": "default",
        "cssClass": "",
        "remark": "",
        "createTime": 1752549568000
      },
      {
        "id": 3005,
        "sort": 3,
        "label": "个人申报",
        "value": "个人申报",
        "dictType": "talent_sources",
        "status": 0,
        "colorType": "default",
        "cssClass": "",
        "remark": "",
        "createTime": 1752549556000
      },
      {
        "id": 3004,
        "sort": 2,
        "label": "机构录入",
        "value": "机构录入",
        "dictType": "talent_sources",
        "status": 0,
        "colorType": "default",
        "cssClass": "",
        "remark": "",
        "createTime": 1752549542000
      },
      {
        "id": 3003,
        "sort": 1,
        "label": "平台录入",
        "value": "平台录入",
        "dictType": "talent_sources",
        "status": 0,
        "colorType": "default",
        "cssClass": "",
        "remark": "",
        "createTime": 1752549529000
      }
    ],
    "total": 5
  },
  "msg": ""
}
```

---
