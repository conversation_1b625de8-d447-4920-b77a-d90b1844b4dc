package cn.bztmaster.cnt.module.publicbiz.api.business.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "商机中心 - 商机跟进新增/更新 Request DTO")
public class BusinessFollowupSaveReqDTO {
    @Schema(description = "跟进ID", example = "1")
    private Long id;

    @Schema(description = "商机ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "商机ID不能为空")
    private Long businessId;

    @Schema(description = "跟进内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "跟进内容不能为空")
    private String content;

    @Schema(description = "跟进时间")
    private String followupTime;

    @Schema(description = "创建人ID")
    private Long creatorId;

    @Schema(description = "创建人名称")
    private String creatorName;
} 