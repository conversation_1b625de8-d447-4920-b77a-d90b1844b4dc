package cn.bztmaster.cnt.module.infra.job.logger;

import cn.bztmaster.cnt.framework.tenant.core.aop.TenantIgnore;
import cn.bztmaster.cnt.module.infra.service.logger.ApiAccessLogService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 物理删除 N 天前的访问日志的 Job
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class AccessLogCleanJob {

    @Resource
    private ApiAccessLogService apiAccessLogService;

    /**
     * 清理超过（14）天的日志
     */
    private static final Integer JOB_CLEAN_RETAIN_DAY = 14;

    /**
     * 每次删除间隔的条数，如果值太高可能会造成数据库的压力过大
     */
    private static final Integer DELETE_LIMIT = 100;

    @XxlJob("accessLogCleanJob")
    @TenantIgnore
    public void execute() {
        Integer count = apiAccessLogService.cleanAccessLog(JOB_CLEAN_RETAIN_DAY, DELETE_LIMIT);
        log.info("[execute][定时执行清理访问日志数量 ({}) 个]", count);
    }

}
