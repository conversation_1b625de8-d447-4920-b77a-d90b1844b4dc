package cn.bztmaster.cnt.module.system.service.material;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;

public interface MaterialArticleService {
    PageResult<ArticleRespVO> getArticlePage(ArticlePageReqVO reqVO);
    Long createArticle(ArticleSaveReqVO reqVO);
    void updateArticle(ArticleSaveReqVO reqVO);
    void deleteArticle(Long id);
    ArticleRespVO getArticleDetail(Long id);
    PageResult<ArticleRespVO> getArticleRecyclePage(ArticlePageReqVO reqVO, String updateTimeFrom, String updateTimeTo);
    void restoreArticleFromRecycle(java.util.List<Long> idList);
    void deleteArticleFromRecycle(java.util.List<Long> idList);
} 