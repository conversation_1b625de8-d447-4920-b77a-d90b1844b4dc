package cn.bztmaster.cnt.module.system.api.material.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "素材分类 - 编辑请求 DTO")
public class CategoryUpdateReqDTO {
    @Schema(description = "分类ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "分类ID不能为空")
    private Long id;

    @Schema(description = "分类名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "分类名称不能为空")
    private String name;

    @Schema(description = "父分类ID，默认0")
    private Long parentId = 0L;

    @Schema(description = "排序，数字越小越靠前，默认0")
    private Integer sort = 0;

    @Schema(description = "状态，0-禁用，1-启用，默认1")
    private Integer status = 1;

    @Schema(description = "分类描述")
    private String description;

    @Schema(description = "可视范围机构ID")
    private Long visibleOrgId;

    @Schema(description = "可视范围机构名称")
    private String visibleOrgName;
} 