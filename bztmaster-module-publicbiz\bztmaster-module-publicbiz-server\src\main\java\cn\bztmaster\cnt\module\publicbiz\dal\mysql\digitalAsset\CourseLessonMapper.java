package cn.bztmaster.cnt.module.publicbiz.dal.mysql.digitalAsset;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset.CourseLessonDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 课程课时 Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface CourseLessonMapper extends BaseMapperX<CourseLessonDO> {

    /**
     * 根据章节ID查询课时列表
     */
    default List<CourseLessonDO> selectListByChapterId(Long chapterId) {
        return selectList(new LambdaQueryWrapperX<CourseLessonDO>()
                .eq(CourseLessonDO::getChapterId, chapterId)
                .orderByAsc(CourseLessonDO::getSortOrder)
                .orderByAsc(CourseLessonDO::getId));
    }

    /**
     * 根据课程ID查询课时列表
     */
    default List<CourseLessonDO> selectListByCourseId(Long courseId) {
        return selectList(new LambdaQueryWrapperX<CourseLessonDO>()
                .eq(CourseLessonDO::getCourseId, courseId)
                .orderByAsc(CourseLessonDO::getSortOrder)
                .orderByAsc(CourseLessonDO::getId));
    }

    /**
     * 根据章节ID和课时标题查询（用于重复性校验）
     */
    CourseLessonDO selectByChapterIdAndTitle(@Param("chapterId") Long chapterId, @Param("title") String title, @Param("excludeId") Long excludeId);

    /**
     * 根据章节ID统计课时数量
     */
    Integer countByChapterId(@Param("chapterId") Long chapterId);

    /**
     * 根据课程ID统计课时数量
     */
    Integer countByCourseId(@Param("courseId") Long courseId);

    /**
     * 获取章节下一个排序序号
     */
    Integer getNextSortOrder(@Param("chapterId") Long chapterId);

    /**
     * 批量删除章节下的所有课时
     */
    void deleteByChapterId(@Param("chapterId") Long chapterId);

    /**
     * 批量删除课程下的所有课时
     */
    void deleteByCourseId(@Param("courseId") Long courseId);

    /**
     * 查询免费试看的课时列表
     */
    List<CourseLessonDO> selectFreeListByCourseId(@Param("courseId") Long courseId);
}
