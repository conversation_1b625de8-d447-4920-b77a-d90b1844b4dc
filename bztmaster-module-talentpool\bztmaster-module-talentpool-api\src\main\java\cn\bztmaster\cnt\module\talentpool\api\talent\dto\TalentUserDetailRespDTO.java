package cn.bztmaster.cnt.module.talentpool.api.talent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;
import java.util.Date;

@Data
public class TalentUserDetailRespDTO {
    @Schema(description = "主表信息")
    private TalentUserRespDTO user;
    @Schema(description = "教育背景列表")
    private List<EducationItem> educationList;
    @Schema(description = "校园实践列表")
    private List<CampusPracticeItem> campusPracticeList;
    @Schema(description = "实习经历列表")
    private List<InternshipItem> internshipList;
    @Schema(description = "项目经历列表")
    private List<ProjectItem> projectList;
    @Schema(description = "培训记录列表")
    private List<TrainingItem> trainingList;
    @Schema(description = "技能清单列表")
    private List<SkillItem> skillList;
    @Schema(description = "认证与资质列表")
    private List<CertificateItem> certificateList;
    @Schema(description = "求职记录列表")
    private List<JobApplicationItem> jobApplicationList;
    @Schema(description = "工作履历列表")
    private List<EmploymentItem> employmentList;
    @Schema(description = "用户标签明细")
    private List<UserTagItem> userTagList;
    @Schema(description = "生命周期时间轴")
    private List<TimelineItem> timelineList;
    @Schema(description = "用户评价列表")
    private List<CommentItem> commentList;

    @Data
    public static class EducationItem {
        private Long educationId;
        private String institution;
        private String degreeType;
        private String major;
        private String startDate;
        private String endDate;
        private String academicRanking;
    }

    @Data
    public static class CampusPracticeItem {
        private Long practiceId;
        private String practiceName;
        private String organizer;
        private String startDate;
        private String endDate;
        private String practiceReport;
    }

    @Data
    public static class InternshipItem {
        private Long internshipId;
        private String company;
        private String position;
        private String startDate;
        private String endDate;
        private String responsibilities;
    }

    @Data
    public static class ProjectItem {
        private Long projectId;
        private String name;
        private String description;
    }

    @Data
    public static class TrainingItem {
        private Long trainingId;
        private String provider;
        private String course;
        private String completeDate;
    }

    @Data
    public static class SkillItem {
        private Long skillId;
        private String name;
        private String level;
    }

    @Data
    public static class CertificateItem {
        private Long certificateId;
        private String name;
        private String issuer;
        private String issueDate;
        private String source;
        private String status;
        private String certificateNo;
        private String expiryDate;
        private String certificateImageUrl;
    }

    @Data
    public static class JobApplicationItem {
        private Long applicationId;
        private String company;
        private String position;
        private String applyDate;
        private String status;
    }

    @Data
    public static class EmploymentItem {
        private Long employmentId;
        private String company;
        private String position;
        private String startDate;
        private String endDate;
        private Integer salary;
    }

    @Data
    public static class UserTagItem {
        private Long userTagId;
        private Long tagId;
        private Long tagTypeId;
        private String tagName;
        private String tagTypeName;
    }

    @Data
    public static class TimelineItem {
        private String type;
        private String content;
        private String date;
    }

    @Data
    public static class CommentItem {
        private String role;
        private String name;
        private String orderNo;
        private String service;
        private String course;
        private String date;
        private Integer score;
        private String content;
    }
}