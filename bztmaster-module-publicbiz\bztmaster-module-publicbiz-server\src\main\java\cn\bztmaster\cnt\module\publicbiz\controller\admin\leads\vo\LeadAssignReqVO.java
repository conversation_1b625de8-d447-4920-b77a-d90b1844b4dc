package cn.bztmaster.cnt.module.publicbiz.controller.admin.leads.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 线索分配 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 线索分配 Request VO")
@Data
public class LeadAssignReqVO {

    @Schema(description = "线索ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "线索ID不能为空")
    private Long id;

    @Schema(description = "跟进人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "跟进人ID不能为空")
    private Long userId;

    @Schema(description = "跟进人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "跟进人姓名不能为空")
    @Size(max = 64, message = "跟进人姓名长度不能超过64")
    private String userName;

    @Schema(description = "分配备注", example = "请尽快跟进")
    private String remark;
}