package cn.bztmaster.cnt.module.publicbiz.service.employment;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageDO;

import javax.validation.Valid;

public interface ServicePackageService {
    Long createServicePackage(@Valid ServicePackageSaveReqVO createReqVO);

    void updateServicePackage(@Valid ServicePackageUpdateReqVO updateReqVO);

    void deleteServicePackage(Long id);

    void moveServicePackageToRecycle(Long id);

    ServicePackageRespVO getServicePackage(Long id);

    PageResult<ServicePackageRespVO> getServicePackagePage(ServicePackagePageReqVO pageReqVO);

    void updateServicePackageStatus(ServicePackageStatusUpdateReqVO reqVO);
} 