package cn.bztmaster.cnt.module.publicbiz.api.leads.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 线索跟进记录 Response DTO
 *
 * <AUTHOR>
 */
@Schema(description = "线索跟进记录响应 DTO")
@Data
public class LeadFollowUpLogRespDTO {

    @Schema(description = "主键，自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "关联的线索ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "LEAD20250721001")
    private String leadId;

    @Schema(description = "跟进内容详情", requiredMode = Schema.RequiredMode.REQUIRED, example = "客户对我们的产品表示很感兴趣，计划下周再次沟通")
    private String followUpContent;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "创建人", example = "admin")
    private String creator;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime updateTime;

    @Schema(description = "更新人", example = "admin")
    private String updater;
}