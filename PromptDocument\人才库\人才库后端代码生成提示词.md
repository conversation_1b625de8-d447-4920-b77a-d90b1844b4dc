---
description: 
globs: 
alwaysApply: false
---
## 1. 自动生成方案说明

### 步骤一：分析 和页面

- 读取 /src/api/apachecore/talentpool/ 下的所有 JS/TS 文件，分析每个API的接口路径、请求方法、参数、返回值。

- 读取 /src/views/apachecore/talentpool/ 下的所有页面，分析页面涉及的业务功能、表单字段、表格展示、操作按钮等。


### 步骤二：页面设计的相关数据表

- \## 1. 人才库中枢（OneID）相关表

 ## 1.1. 用户主表（talent_user）

```sql
CREATE TABLE `talent_user` (
  `user_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `name` VARCHAR(50) NOT NULL COMMENT '姓名',
  `identity_id` VARCHAR(18) NOT NULL UNIQUE COMMENT '身份证号',
  `birth_date` DATE NOT NULL COMMENT '出生日期',
  `gender` VARCHAR(10) COMMENT '性别（可选值：男、女、其他）',
  `phone` VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号',
  `email` VARCHAR(100) COMMENT '邮箱',
  `avatar_url` VARCHAR(255) COMMENT '头像URL',
  `status` VARCHAR(30) COMMENT 'OneID状态（可选值：正常、待合并、已禁用）',
  `register_source` VARCHAR(50) COMMENT '用户来源',
  `oneid` CHAR(36) NOT NULL DEFAULT '' COMMENT 'OneID GUID',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`user_id`)
) COMMENT='平台用户主表';
```

## 1.2 教育背景表（talent_education）

```sql
CREATE TABLE `talent_education` (
  `education_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `institution` VARCHAR(100) NOT NULL COMMENT '毕业院校',
  `college_address` VARCHAR(255) COMMENT '学院地址',
  `degree_type` VARCHAR(20) COMMENT '学位类型（可选值：专科、本科、硕士、博士）',
  `major` VARCHAR(100) COMMENT '专业',
  `start_date` DATE COMMENT '入学时间',
  `end_date` DATE COMMENT '毕业时间',
  `academic_ranking` VARCHAR(50) COMMENT '学业排名',
  `is_internship` TINYINT(1) DEFAULT 0 COMMENT '是否实习 0-否 1-是',
  `internship_type` VARCHAR(20) COMMENT '实习类别（可选值：生产实习、认知实习、毕业实习、其他）',
  `internship_duration` INT COMMENT '实习时长（天）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='教育背景';
```

### 1.3 校园实践表（talent_campus_practice）

```sql
CREATE TABLE `talent_campus_practice` (
  `practice_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `practice_name` VARCHAR(100) NOT NULL COMMENT '实践名称',
  `organizer` VARCHAR(100) COMMENT '组织方',
  `start_date` DATE COMMENT '开始时间',
  `end_date` DATE COMMENT '结束时间',
  `practice_report` TEXT COMMENT '实践报告/总结',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='校园实践';
```

### 1.4 校园实践表（talent_campus_practice）

```sql
CREATE TABLE `talent_campus_practice` (
  `practice_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `practice_name` VARCHAR(100) NOT NULL COMMENT '实践名称',
  `organizer` VARCHAR(100) COMMENT '组织方',
  `start_date` DATE COMMENT '开始时间',
  `end_date` DATE COMMENT '结束时间',
  `practice_report` TEXT COMMENT '实践报告/总结',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='校园实践';
```

### 1.5 实习经历表（talent_internship）

```sql
CREATE TABLE `talent_internship` (
  `internship_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `company` VARCHAR(100) NOT NULL COMMENT '实习公司',
  `position` VARCHAR(100) COMMENT '实习岗位',
  `start_date` DATE COMMENT '开始时间',
  `end_date` DATE COMMENT '结束时间',
  `responsibilities` TEXT COMMENT '工作职责',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='实习经历';
```

### 1.6 项目经历表（talent_project）

```sql
CREATE TABLE `talent_project` (
  `project_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `name` VARCHAR(100) NOT NULL COMMENT '项目名称',
  `description` TEXT COMMENT '项目描述',
  `start_date` DATE COMMENT '开始时间',
  `end_date` DATE COMMENT '结束时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='项目经历';
```

### 1.7 培训记录表（talent_training）

```sql
CREATE TABLE `talent_training` (
  `training_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `provider` VARCHAR(100) COMMENT '培训机构',
  `course` VARCHAR(100) COMMENT '课程名称',
  `complete_date` DATE COMMENT '完成日期',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='培训记录';
```

### 1.8 技能清单表（talent_skill）

```sql
CREATE TABLE `talent_skill` (
  `skill_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `name` VARCHAR(100) NOT NULL COMMENT '技能名称',
  `level` VARCHAR(10) COMMENT '掌握程度（可选值：了解、熟悉、精通）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='技能清单';
```

### 1.9 认证与资质表（talent_certificate）

```sql
CREATE TABLE `talent_certificate` (
  `certificate_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `name` VARCHAR(100) NOT NULL COMMENT '证书名称',
  `issuer` VARCHAR(100) COMMENT '发证机构',
  `issue_date` DATE COMMENT '颁发日期',
  `source` VARCHAR(20) COMMENT '记录来源（可选值：PLATFORM、AGENCY、SELF）',
  `status` VARCHAR(30) COMMENT '审核状态（可选值：VERIFIED、PENDING_VERIFICATION、REJECTED）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='认证与资质';
```

### 1.10 求职记录表（talent_job_application）

```sql
CREATE TABLE `talent_job_application` (
  `application_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `company` VARCHAR(100) NOT NULL COMMENT '申请公司',
  `position` VARCHAR(100) COMMENT '申请职位',
  `apply_date` DATE COMMENT '申请日期',
  `status` VARCHAR(30) COMMENT '状态（可选值：已投递、面试中、已录用、未通过）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='求职记录';
```

### 1.11 工作履历表（talent_employment）

```sql
CREATE TABLE `talent_employment` (
  `employment_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `company` VARCHAR(100) NOT NULL COMMENT '就职公司',
  `position` VARCHAR(100) COMMENT '职位',
  `start_date` DATE COMMENT '入职时间',
  `end_date` DATE COMMENT '离职时间',
  `salary` DECIMAL(10,2) COMMENT '薪资',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='工作履历';
```

### 1.12 人才标签表（talent_user_tag）

```sql
CREATE TABLE `talent_user_tag` (
  `user_tag_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `tag_id` BIGINT NOT NULL COMMENT '标签ID，关联标签库',
  `tag_type_id` BIGINT NOT NULL COMMENT '标签类型ID',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='用户标签关联表';
```

### 1.13 标签类型表（talent_tag_type）

```sql
CREATE TABLE `talent_tag_type` (
  `tag_type_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '标签类型ID',
  `type_code` VARCHAR(50) NOT NULL UNIQUE COMMENT '标签类型编码',
  `type_name` VARCHAR(100) NOT NULL COMMENT '标签类型名称',
  `description` VARCHAR(255) COMMENT '描述',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
) COMMENT='标签类型表';
```

## 1.14. 标签库表（talent_tag_library）

```sql
CREATE TABLE `talent_tag_library` (
  `tag_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '标签ID',
  `tag_type_id` BIGINT NOT NULL COMMENT '标签类型ID',
  `tag_code` VARCHAR(50) NOT NULL UNIQUE COMMENT '标签编码',
  `tag_name` VARCHAR(100) NOT NULL COMMENT '标签名称',
  `description` VARCHAR(255) COMMENT '标签描述',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'

) COMMENT='标签库表';
```

  \```

### 步骤三：确定后端接口与分层结构

- 每个API和页面功能，生成一个对应的后端Controller（RESTful风格）。

- 每个业务实体，生成VO/DTO、Service接口与实现、Mapper接口（如涉及数据库）、Convert类、枚举、API接口等。

- 目录结构严格参考 bztmaster-module-pay，如：

  bztmaster-module-talentpool/
  └── bztmaster-module-talentpool-api/
      └── src/main/java/cn/bztmaster/cnt/module/talentpool/
          ├── api/
          │   └── talent/
          │       ├── TalentUserApi.java
          │       ├── TalentUserDTO.java
          │       ├── TalentUserPageReqDTO.java
          │       ├── TalentUserRespDTO.java
          │       └── ...（各子表DTO/Feign接口）
          └── enums/
              └── talent/
                  ├── TalentUserStatusEnum.java
                  └── ...（各子表枚举/常量）

    └── bztmaster-module-talentpool-server/
        └── src/main/java/cn/bztmaster/cnt/module/talentpool/
            ├── controller/
            ├── service/
            ├── service/impl/
            ├── dal/
            ├── dal/dataobject/
            ├── dal/mysql/
            ├── vo/
            ├── convert/
            ├── enums/
            └── api/

### 步骤四：自动生成代码

- Controller：每个API生成一个对应的Controller方法，路径、方法、参数与前端一致，带有Swagger注解。

- VO/DTO：根据前端参数和表单字段生成请求VO、响应VO，带有参数校验注解。

- Service/Impl：生成接口和实现，包含CRUD和具体的业务方法，方法注释详细。

- Mapper/DO：如涉及数据库，生成MyBatis Mapper接口和DO实体，字段与VO/DTO一致。

- Convert：生成VO与DO、DTO的转换类。

- Enums：如有枚举字段，自动生成枚举类。

- API接口：如有远程调用需求，生成Feign接口。

- 所有类和方法都带有详细注释，对应DTO、VO与DO对象也生成详细注释，便于初学者理解。

- 前端与后端对应的字段信息一定要确保能对应一致。

  

### 步骤五：需要注意的业务逻辑点

- 如果在维护人才信息时，不是选择的现有标签数据而是手动编写的新的标签数据则需要在下沉人才相关信息时添加新的标签数据并生成人才与标签关联的数据。
- 合并人才数据时需要按照以下逻辑进行合并：
- 核心原则：应对多手机号场景 (新增说明)**
  - 本方案充分考虑到同一物理用户可能拥有多个手机号的普遍情况。其核心设计思想是：**以`身份证号 (identity_id)`作为识别物理用户的唯一、最终锚点，而将手机号、微信UnionID等作为过程中的关联线索。**
  - 一个用户使用不同手机号注册，初期会形成多个独立的`user_id`。但只要该用户在任一业务场景下完成实名认证，系统便会触发下述的"一级识别"规则，将不同手机号关联的账户自动、强制地合并到首次完成实名认证的那个主`OneID`账户下，从而确保数据的最终完整性与唯一性。
- **一级识别：强制合并 (身份证号)**
  - **字段:** `identity_id`
  - **规则:** 身份证号是识别中国大陆用户的最强、最稳定标识。在用户进行实名认证并录入身份证号后，系统应立即在`USER`表中进行全局唯一性校验。
  - **触发场景:** 用户进行实名认证、报名需强实名的考试或培训、签署电子合同等。
  - **合并逻辑:**
    - **IF** 新录入的`identity_id`在系统中已存在（关联于`User_A`），而当前用户是`User_B`。
    - **THEN** 系统自动执行强制合并。将`User_B`的所有关联数据（如订单、学习记录、认证等）迁移至`User_A`名下，并将`User_B`的账户状态置为"已合并"或"已停用"。所有后续登录应导向`User_A`。
- **二级识别：强关联合并 (手机号)**
  - **字段:** `phone`
  - **规则:** 手机号是核心的登录凭证和常用的关联字段。
  - **触发场景:** 用户注册、登录、绑定新手机号。
  - **合并逻辑:**
    - **IF** 用户尝试将手机号`P`绑定到`User_B`，但`P`已被`User_A`验证并绑定。
    - **THEN** 系统应提示用户："该手机号已被其他账号绑定，是否要将两个账号信息合并？"
    - **Action:** 用户确认后，执行与一级识别类似的合并流程。若用户否认，则绑定失败。
- **三级识别：弱关联提醒 (微信UnionID)**
  - **字段:** 微信生态下的`UnionID`。
  - **规则:** UnionID能识别同一微信开放平台下的不同应用（小程序、公众号、App）中的同一个微信用户。
  - **触发场景:** 用户使用微信授权登录不同的业务小程序。
  - **合并逻辑:**
    - **IF** `User_A`（通过小程序A登录）和`User_B`（通过小程序B登录）的`UnionID`相同，但`user_id`不同。
    - **THEN** 系统不进行自动合并，因为可能存在家人共用微信的情况。
    - **Action:** 系统应在后台生成一个"疑似关联账户"的提醒，推送给运营人员。同时，可以在用户个人中心提供一个"关联账户合并"的入口，让用户自主选择是否合并。
- **四级识别：人工辅助 (姓名 + 其他信息组合)**
  - **规则:** 仅作为运营人员手动处理数据问题的辅助手段。
  - **触发场景:** 客服接到用户反馈、运营人员数据清洗。
  - **合并逻辑:**
    - **IF** 运营人员发现两个用户（`User_A`, `User_B`）的【姓名 + 毕业院校 + 专业】或【姓名 + 银行卡号】等组合信息高度一致。
    - **THEN** 运营人员可发起"人工合并流程"。
    - **Action:**
      1. 联系用户，通过电话或在线方式核实身份。
      2. 在用户确认授权后，于后台执行手动数据合并操作。
      3. 所有人工合并操作必须留下详细的操作日志，记录操作人、时间和原因。

### 步骤六：自动补全与TODO

- 对于前端未明确的字段类型、业务逻辑，自动推断或以// TODO注释标记，便于后续补充。

- 生成后的代码直接更新到指定的项目bztmaster-module-talentpool模块下对应的目录中。 

人才库分页接口需要调整逻辑，目前没有返回主键id和标签的信息
接口地址：/apachecore/talentpool/page
1. 主键id：取人才库表talent_user的user_id字段
2. 标签信息取值逻辑：用人才库表 talent_user 字段user_id 关联 talent_user_tag的user_id字段，再用talent_user_tag的tag_id字段关联标签库表
   talent_tag_library的tag_id字段，返回当前这个人的标签名字
3. 入参传递的 tag 需要过滤人才库的标签信息。
