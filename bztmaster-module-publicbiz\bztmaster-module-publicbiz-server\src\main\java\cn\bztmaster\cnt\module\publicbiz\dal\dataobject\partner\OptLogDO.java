package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.partner;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;

@Data
@TableName("publicbiz_opt_log")
@Schema(description = "资源中心 - 操作日志表 DO")
public class OptLogDO extends BaseDO {
    /** 主键 */
    @TableId
    private Long id;
    /** 业务类型（如partner/teacher/course/exam/venue等） */
    private String bizType;
    /** 业务主表ID */
    private Long bizId;
    /** 操作内容 */
    private String content;
    /** 操作人 */
    private String operator;
    /** 操作时间 */
    private Date operateTime;
    /** 租户编号 */
    private Long tenantId;
}