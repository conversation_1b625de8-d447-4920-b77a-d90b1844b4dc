---
description: 
globs: 
alwaysApply: false
---
Read file: README.md
Read file: src/api/apachecore/talentpool/index.ts
Read file: src/views/apachecore/talentpool/index.vue
Read file: src/views/apachecore/talentpool/TalentDetail.vue
根据你提供的人才库相关表结构和前端代码实现（API接口、页面、详情页），
我为你整理了一份详细的接口文档。文档内容包括接口说明、请求方式、入参、出参字段及类型、请求示例和返回示例，帮助你或开发者理解和对接这些接口。

---

# 人才库（OneID）接口文档

## 1. 人才分页查询

- **接口地址**：`GET /apachecore/talentpool/page`
- **功能说明**：分页查询人才库用户列表，可按关键字、来源、标签、状态等筛选。
- **请求参数**：

| 参数名      | 类型     | 必填 | 说明                   |
| ----------- | -------- | ---- | ---------------------- |
| keyword     | string   | 否   | 关键字（姓名/手机号/身份证） |
| source      | string   | 否   | 用户来源               |
| tag         | string   | 否   | 用户标签               |
| status      | string   | 否   | 用户状态（正常/待合并/已禁用） |
| pageNo      | number   | 否   | 页码，默认1            |
| pageSize    | number   | 否   | 每页条数，默认10        |

- **返回字段**：

| 字段名        | 类型     | 说明                   |
| ------------- | -------- | ---------------------- |
| id            | number   | 用户ID                 |
| oneid         | string   | oneid                 |
| name          | string   | 姓名                   |
| phone         | string   | 手机号                 |
| idCard        | string   | 身份证号               |
| source        | string   | 用户来源               |
| tags          | string[] | 用户标签               |
| status        | string   | 用户状态               |
| completeness  | number   | 档案完整度（0-100）    |

- **请求示例**：

```http
GET /apachecore/talentpool/page?keyword=张三&pageNo=1&pageSize=10
```

- **返回示例**：

```json
{
  "total": 2,
  "list": [
    {
      "id": 1,
       "oneid": 10001,
      "name": "张三",
      "phone": "138****1234",
      "idCard": "110101199001011234",
      "source": "高校实习小程序",
      "tags": ["认证:高级母婴护理", "已实名"],
      "status": "正常",
      "completeness": 95
    },
    {
      "id": 2,
      "oneid": 10002,
      "name": "李四",
      "phone": "139****5678",
      "idCard": "110101199002021234",
      "source": "家政服务员注册",
      "tags": ["学历:本科", "已实名"],
      "status": "正常",
      "completeness": 70
    }
  ]
}
```

---

## 2. 人才详情查询

- **接口地址**：`GET /apachecore/talentpool/get`
- **功能说明**：根据ID获取人才详细信息（含基本信息、教育、实习、项目、培训、技能、证书、求职、工作、标签等）。
- **请求参数**：

| 参数名 | 类型   | 必填 | 说明   |
| ------ | ------ | ---- | ------ |
| id     | number | 是   | 用户ID |

- **返回字段**（示例结构，具体字段可根据后端返回补充）：

```json
{
  "id": 10001,
  "name": "张三",
  "phone": "138****1234",
  "idCard": "110101199001011234",
  "email": "<EMAIL>",
  "gender": "女",
  "birthDate": "1990-03-07",
  "avatarUrl": "https://xxx.com/avatar.jpg",
  "status": "正常",
  "source": "高校实习小程序",
  "oneid": "xxxx-xxxx-xxxx-xxxx",
  "tags": ["认证:高级母婴护理", "已实名"],
  "completeness": 95,
  "orgId": 1,
  "orgName": "XX家政",
  "talentSource": "平台推荐",
  "isSelfSupport": true,
  "educationList": [
    {
      "educationId": 1,
      "institution": "XX大学",
      "degreeType": "本科",
      "major": "护理学",
      "startDate": "2018-09-01",
      "endDate": "2022-06-30",
      "academicRanking": "Top 10%"
    }
  ],
  "campusPracticeList": [
    {
      "practiceId": 1,
      "practiceName": "XX大学暑期社会实践",
      "organizer": "XX大学团委",
      "startDate": "2021-07-01",
      "endDate": "2021-08-31",
      "practiceReport": "总结内容"
    }
  ],
  "internshipList": [
    {
      "internshipId": 1,
      "company": "爱心家政",
      "position": "实习月嫂",
      "startDate": "2023-05-01",
      "endDate": "2023-08-01",
      "responsibilities": "负责新生儿护理"
    }
  ],
  "projectList": [
    {
      "projectId": 1,
      "name": "个人作品集网站",
      "description": "使用React和Node.js开发"
    }
  ],
  "trainingList": [
    {
      "trainingId": 1,
      "provider": "汇成平台",
      "course": "金牌月嫂培训课程",
      "completeDate": "2023-12-20"
    }
  ],
  "skillList": [
    {
      "skillId": 1,
      "name": "催乳",
      "level": "精通"
    }
  ],
  "certificateList": [
    {
      "certificateId": 1,
      "name": "金牌月嫂证书",
      "issuer": "汇成平台认证",
      "issueDate": "2024-01-15",
      "expiryDate": "2026-01-15",
      "certificateNo": "JPYS20240115001",
      "certificateImageUrl": "https://xxx.com/cert.jpg",
      "source": "PLATFORM",
      "status": "VERIFIED"
    }
  ],
  "jobApplicationList": [
    {
      "applicationId": 1,
      "company": "XX家政公司",
      "position": "高级育儿嫂",
      "applyDate": "2024-01-20",
      "status": "面试中"
    }
  ],
  "employmentList": [
    {
      "employmentId": 1,
      "company": "爱心家政服务",
      "position": "金牌月嫂",
      "startDate": "2024-02-01",
      "endDate": null,
      "salary": 12000
    }
  ],
  "userTagList": [
    {
      "userTagId": 1,
      "tagId": 101,
      "tagTypeId": 1,
      "tagName": "已实名",
      "tagTypeName": "认证"
    }
  ]
}
```

- **请求示例**：

```http
GET /apachecore/talentpool/get?id=10001
```

---

## 3. 编辑人才信息

- **接口地址**：`PUT /apachecore/talentpool/update`
- **功能说明**：编辑人才基本信息。
- **请求参数**（JSON Body）：

| 字段名   | 类型     | 必填 | 说明     |
| -------- | -------- | ---- | -------- |
| id       | number   | 是   | 用户ID   |
| name     | string   | 是   | 姓名     |
| phone    | string   | 是   | 手机号   |
| idCard   | string   | 是   | 身份证号 |
| source   | string   | 否   | 用户来源 |
| tags     | string[] | 否   | 用户标签 |
| status   | string   | 否   | 用户状态 |
| completeness | number | 否 | 档案完整度 |

- **请求示例**：

```json
PUT /apachecore/talentpool/update
{
  "id": 10001,
  "name": "张三",
  "phone": "138****1234",
  "idCard": "110101199001011234",
  "source": "高校实习小程序",
  "tags": ["认证:高级母婴护理", "已实名"],
  "status": "正常",
  "completeness": 95
}
```

- **返回示例**：

```json
{
  "success": true
}
```

---

## 4. 停用人才

- **接口地址**：`POST /apachecore/talentpool/disable`
- **功能说明**：将指定人才状态设为"已禁用"。
- **请求参数**：

| 参数名 | 类型   | 必填 | 说明   |
| ------ | ------ | ---- | ------ |
| id     | number | 是   | 用户ID |

- **请求示例**：

```http
POST /apachecore/talentpool/disable?id=10001
```

- **返回示例**：

```json
{
  "success": true
}
```

---

## 5. 合并人才

- **接口地址**：`POST /apachecore/talentpool/merge`
- **功能说明**：对待合并状态的人才进行合并操作。
- **请求参数**：

| 参数名 | 类型   | 必填 | 说明   |
| ------ | ------ | ---- | ------ |
| id     | number | 是   | 用户ID |

- **请求示例**：

```http
POST /apachecore/talentpool/merge?id=10003
```

- **返回示例**：

```json
{
  "success": true
}
```

---

## 6. 新增人才档案

- **接口地址**：`POST /apachecore/talentpool/create`
- **功能说明**：新增一条人才库档案，支持填写基本信息、教育、实习、项目、培训、技能、证书、求职、工作、标签等完整档案内容。

- **请求参数**（JSON Body）：

| 字段名              | 类型           | 必填 | 说明                         |
|---------------------|----------------|------|------------------------------|
| name                | string         | 是   | 姓名                         |
| phone               | string         | 是   | 手机号                       |
| idCard              | string         | 是   | 身份证号                     |
| email               | string         | 否   | 邮箱                         |
| gender              | string         | 否   | 性别（男/女）                |
| birthDate           | string         | 否   | 出生日期（yyyy-MM-dd）       |
| avatarUrl           | string         | 否   | 头像URL                      |
| source              | string         | 否   | 用户来源                     |
| tags                | string[]       | 否   | 用户标签                     |
| status              | string         | 否   | 用户状态（正常/待合并/已禁用）|
| completeness        | number         | 否   | 档案完整度（0-100）          |
| orgId             | number        | 否   | 所属机构ID                  |
| orgName           | string        | 否   | 所属机构名称                |
| talentSource      | string        | 否   | 人才来源                    |
| isSelfSupport     | boolean       | 否   | 是否平台自营（true-是，false-否） |
| educationList       | object[]       | 否   | 教育背景列表                 |
| campusPracticeList  | object[]       | 否   | 校园实践列表                 |
| internshipList      | object[]       | 否   | 实习经历列表                 |
| projectList         | object[]       | 否   | 项目经历列表                 |
| trainingList        | object[]       | 否   | 培训经历列表                 |
| skillList           | object[]       | 否   | 技能列表                     |
| certificateList     | object[]       | 否   | 证书列表                     |
| jobApplicationList  | object[]       | 否   | 求职申请列表                 |
| employmentList      | object[]       | 否   | 工作经历列表                 |
| userTagList         | object[]       | 否   | 用户标签明细                 |

**各子表字段说明（与详情接口一致，参考如下）：**

- `educationList`：

  | 字段名           | 类型   | 必填 | 说明         |
  |------------------|--------|------|--------------|
  | institution      | string | 是   | 学校名称     |
  | degreeType       | string | 是   | 学位类型     |
  | major            | string | 否   | 专业         |
  | startDate        | string | 否   | 入学时间     |
  | endDate          | string | 否   | 毕业时间     |
  | academicRanking  | string | 否   | 学业排名     |

- `campusPracticeList`：

  | 字段名           | 类型   | 必填 | 说明         |
  |------------------|--------|------|--------------|
  | practiceName     | string | 是   | 实践名称     |
  | organizer        | string | 否   | 组织方       |
  | startDate        | string | 否   | 开始时间     |
  | endDate          | string | 否   | 结束时间     |
  | practiceReport   | string | 否   | 实践总结     |

- `internshipList`：

  | 字段名           | 类型   | 必填 | 说明         |
  |------------------|--------|------|--------------|
  | company          | string | 是   | 实习公司     |
  | position         | string | 是   | 实习岗位     |
  | startDate        | string | 否   | 开始时间     |
  | endDate          | string | 否   | 结束时间     |
  | responsibilities | string | 否   | 职责描述     |

- `projectList`：

  | 字段名           | 类型   | 必填 | 说明         |
  |------------------|--------|------|--------------|
  | name             | string | 是   | 项目名称     |
  | description      | string | 否   | 项目描述     |

- `trainingList`：

  | 字段名           | 类型   | 必填 | 说明         |
  |------------------|--------|------|--------------|
  | provider         | string | 是   | 培训机构     |
  | course           | string | 是   | 培训课程     |
  | completeDate     | string | 否   | 完成日期     |

- `skillList`：

  | 字段名           | 类型   | 必填 | 说明         |
  |------------------|--------|------|--------------|
  | name             | string | 是   | 技能名称     |
  | level            | string | 否   | 掌握程度     |

- `certificateList`：

  | 字段名           | 类型   | 必填 | 说明         |
  |------------------|--------|------|--------------|
  | name             | string | 是   | 证书名称     |
  | issuer           | string | 否   | 发证机构     |
  | issueDate        | string | 否   | 颁发日期     |
  | expiryDate       | string | 否   | 到期时间     |
  | certificateNo    | string | 否   | 证书编号     |
  | certificateImageUrl | string | 否 | 证书图片     |
  | source           | string | 否   | 记录来源     |
  | status           | string | 否   | 审核状态     |

- `jobApplicationList`：

  | 字段名           | 类型   | 必填 | 说明         |
  |------------------|--------|------|--------------|
  | company          | string | 是   | 申请公司     |
  | position         | string | 是   | 申请岗位     |
  | applyDate        | string | 否   | 申请日期     |
  | status           | string | 否   | 状态         |

- `employmentList`：

  | 字段名           | 类型   | 必填 | 说明         |
  |------------------|--------|------|--------------|
  | company          | string | 是   | 工作单位     |
  | position         | string | 是   | 职位         |
  | startDate        | string | 否   | 入职时间     |
  | endDate          | string | 否   | 离职时间     |
  | salary           | number | 否   | 薪资         |

- `userTagList`：

  | 字段名           | 类型   | 必填 | 说明         |
  |------------------|--------|------|--------------|
  | tagId            | number | 是   | 标签ID       |
  | tagTypeId        | number | 否   | 标签类型ID   |
  | tagName          | string | 是   | 标签名称     |
  | tagTypeName      | string | 否   | 标签类型名称 |

- **请求示例**：

```json
POST /apachecore/talentpool/create
{
  "name": "张三",
  "phone": "13812345678",
  "idCard": "110101199001011234",
  "email": "<EMAIL>",
  "gender": "女",
  "birthDate": "1990-03-07",
  "avatarUrl": "https://xxx.com/avatar.jpg",
  "source": "高校实习小程序",
  "tags": ["认证:高级母婴护理", "已实名"],
  "status": "正常",
  "completeness": 95,
  "orgId": 1,
  "orgName": "XX家政",
  "talentSource": "平台推荐",
  "isSelfSupport": true,
  "educationList": [
    {
      "institution": "XX大学",
      "degreeType": "本科",
      "major": "护理学",
      "startDate": "2018-09-01",
      "endDate": "2022-06-30",
      "academicRanking": "Top 10%"
    }
  ],
  "campusPracticeList": [
    {
      "practiceName": "XX大学暑期社会实践",
      "organizer": "XX大学团委",
      "startDate": "2021-07-01",
      "endDate": "2021-08-31",
      "practiceReport": "总结内容"
    }
  ],
  "internshipList": [
    {
      "company": "爱心家政",
      "position": "实习月嫂",
      "startDate": "2023-05-01",
      "endDate": "2023-08-01",
      "responsibilities": "负责新生儿护理"
    }
  ],
  "projectList": [
    {
      "name": "个人作品集网站",
      "description": "使用React和Node.js开发"
    }
  ],
  "trainingList": [
    {
      "provider": "汇成平台",
      "course": "金牌月嫂培训课程",
      "completeDate": "2023-12-20"
    }
  ],
  "skillList": [
    {
      "name": "催乳",
      "level": "精通"
    }
  ],
  "certificateList": [
    {
      "name": "金牌月嫂证书",
      "issuer": "汇成平台认证",
      "issueDate": "2024-01-15",
      "expiryDate": "2026-01-15",
      "certificateNo": "JPYS20240115001",
      "certificateImageUrl": "https://xxx.com/cert.jpg",
      "source": "PLATFORM",
      "status": "VERIFIED"
    }
  ],
  "jobApplicationList": [
    {
      "company": "XX家政公司",
      "position": "高级育儿嫂",
      "applyDate": "2024-01-20",
      "status": "面试中"
    }
  ],
  "employmentList": [
    {
      "company": "爱心家政服务",
      "position": "金牌月嫂",
      "startDate": "2024-02-01",
      "endDate": null,
      "salary": 12000
    }
  ],
  "userTagList": [
    {
      "tagId": 101,
      "tagTypeId": 1,
      "tagName": "已实名",
      "tagTypeName": "认证"
    }
  ]
}
```

- **返回字段**：

| 字段名   | 类型   | 说明         |
|----------|--------|--------------|
| success  | bool   | 是否成功     |
| id       | number | 新增档案ID   |

- **返回示例**：

```json
{
  "success": true,
  "id": 10005
}
```

---

7. 获取人才画像详情

- **接口地址**：`GET /apachecore/talentpool/detail`
- **功能说明**：根据用户ID获取人才画像详情，包含基本信息、教育背景、校园实践、实习经历、项目经历、培训、技能、证书、求职、工作、标签等完整档案内容。
- **请求参数**：

| 参数名   | 类型   | 必填 | 说明     |
| -------- | ------ | ---- | -------- |
| userId   | number | 是   | 用户ID   |

- **返回字段**：

| 字段名                | 类型         | 说明                       |
|----------------------|--------------|----------------------------|
| user                 | object       | 用户主表信息（talent_user）|
| orgId                | number       | 所属机构ID                 |
| orgName              | string       | 所属机构名称               |
| talentSource         | string       | 人才来源                   |
| isSelfSupport        | boolean      | 是否平台自营               |
| educationList        | object[]     | 教育背景列表               |
| campusPracticeList   | object[]     | 校园实践列表               |
| internshipList       | object[]     | 实习经历列表               |
| projectList          | object[]     | 项目经历列表               |
| trainingList         | object[]     | 培训记录列表               |
| skillList            | object[]     | 技能清单列表               |
| certificateList      | object[]     | 认证与资质列表             |
| jobApplicationList   | object[]     | 求职记录列表               |
| employmentList       | object[]     | 工作履历列表               |
| userTagList          | object[]     | 用户标签明细               |
| timelineList         | object[]     | 生命周期时间轴（按时间倒序，含类型、内容、时间）|
| commentList          | object[]     | 用户评价列表               |

- **请求示例**：

```http
GET /apachecore/talentpool/detail?userId=123
```

- **返回示例**：

```json
{
  "user": {
    "userId": 123,
    "name": "张三",
    "identityId": "110101199001011234",
    "birthDate": "1990-03-07",
    "gender": "女",
    "phone": "13812345678",
    "email": "<EMAIL>",
    "avatarUrl": "https://xxx.com/avatar.jpg",
    "status": "正常",
    "registerSource": "高校实习小程序",
    "oneid": "xxxx-xxxx-xxxx",
    "completeness": 95,
    "orgId": 1,
    "orgName": "XX家政",
    "talentSource": "平台推荐",
    "isSelfSupport": true
  },
  "educationList": [
    {
      "educationId": 1,
      "institution": "XX大学",
      "degreeType": "本科",
      "major": "护理学",
      "startDate": "2018-09-01",
      "endDate": "2022-06-30",
      "academicRanking": "Top 10%"
    }
  ],
  "campusPracticeList": [
    {
      "practiceId": 1,
      "practiceName": "XX大学暑期社会实践",
      "organizer": "XX大学团委",
      "startDate": "2021-07-01",
      "endDate": "2021-08-31",
      "practiceReport": "总结内容"
    }
  ],
  "internshipList": [
    {
      "internshipId": 1,
      "company": "爱心家政",
      "position": "实习月嫂",
      "startDate": "2023-05-01",
      "endDate": "2023-08-01",
      "responsibilities": "负责新生儿护理"
    }
  ],
  "projectList": [
    {
      "projectId": 1,
      "name": "个人作品集网站",
      "description": "使用React和Node.js开发"
    }
  ],
  "trainingList": [
    {
      "trainingId": 1,
      "provider": "汇成平台",
      "course": "金牌月嫂培训课程",
      "completeDate": "2023-12-20"
    }
  ],
  "skillList": [
    {
      "skillId": 1,
      "name": "催乳",
      "level": "精通"
    }
  ],
  "certificateList": [
    {
      "certificateId": 1,
      "name": "金牌月嫂证书",
      "issuer": "汇成平台认证",
      "issueDate": "2024-01-15",
      "expiryDate": "2026-01-15",
      "certificateNo": "JPYS20240115001",
      "certificateImageUrl": "https://xxx.com/cert.jpg",
      "source": "PLATFORM",
      "status": "VERIFIED"
    }
  ],
  "jobApplicationList": [
    {
      "applicationId": 1,
      "company": "XX家政公司",
      "position": "高级育儿嫂",
      "applyDate": "2024-01-20",
      "status": "面试中"
    }
  ],
  "employmentList": [
    {
      "employmentId": 1,
      "company": "爱心家政服务",
      "position": "金牌月嫂",
      "startDate": "2024-02-01",
      "endDate": null,
      "salary": 12000
    }
  ],
  "userTagList": [
    {
      "userTagId": 101,
      "tagId": 101,
      "tagTypeId": 1,
      "tagName": "已实名",
      "tagTypeName": "认证"
    }
  ],
  "timelineList": [
    { "type": "就业", "content": "获得李女士五星好评", "date": "2024-06-10" },
    { "type": "认证", "content": "考取金牌月嫂证书", "date": "2024-01-15" },
    { "type": "培训", "content": "完成金牌月嫂培训课程", "date": "2023-12-20" },
    { "type": "实习", "content": "在爱心家政完成3个月实习", "date": "2023-08-01" },
    { "type": "实践", "content": "参与XX大学暑期社会实践", "date": "2022-07-15" },
    { "type": "教育", "content": "毕业于XX大学", "date": "2022-06-30" }
  ],
  "commentList": [
    {
      "role": "雇主",
      "name": "李女士",
      "orderNo": "20240501A",
      "service": "金牌月嫂",
      "date": "2024-06-10",
      "score": 5,
      "content": "张姐非常专业，有耐心，把我和宝宝都照顾得很好，推荐！"
    },
    {
      "role": "培训师",
      "name": "王老师",
      "course": "金牌月嫂培训",
      "date": "2023-12-25",
      "score": 5,
      "content": "学习态度认真，实践能力强，与同学相处融洽。"
    }
  ]
}
```

---

## 8. 变更人才状态（启用/停用/禁用等）

- **接口地址**：`POST /apachecore/talentpool/change-status`
- **功能说明**：根据用户ID和目标状态变更人才状态（如启用、停用、禁用等）。
- **请求参数**：

| 参数名 | 类型   | 必填 | 说明     |
| ------ | ------ | ---- | -------- |
| id     | number | 是   | 用户ID   |
| status | string | 是   | 目标状态（如“正常”、“停用”、“禁用”等） |

- **请求示例**：

```http
POST /apachecore/talentpool/change-status?id=10001&status=正常
```

- **返回示例**：

```json
{
  "success": true
}
```


9. 文件上传（图片/附件）
接口地址：POST /infra/file/upload
功能说明：上传文件（如图片、附件等），后端接收并存储，返回文件访问地址。适用于人才库头像、证书图片等场景。
请求参数（multipart/form-data）：
| 参数名 | 类型 | 必填 | 说明 |
| --------- | -------------- | ---- | ------------ |
| file | file | 是 | 上传的文件 |
| directory | string | 否 | 存储目录（如avatar、certificate等，便于分类管理） |
请求示例：
以表单方式上传图片：
POST /infra/file/upload
Content-Type: multipart/form-data

file: <选择图片文件>
directory: avatar

返回字段：
| 字段名 | 类型 | 说明 |
|----------|--------|--------------|
| code | number | 业务状态码（0为成功） |
| data | string | 文件访问URL |
| msg | string | 提示信息 |
返回示例：
{
  "code": 0,
  "data": "https://your-domain.com/infra/file/1/get/avatar/2024/06/11/xxx.jpg",
  "msg": "操作成功"
}
备注：
前端需以multipart/form-data方式上传文件。
directory参数建议按业务分类（如avatar、certificate、attachment等），便于后端管理。
返回的data字段即为文件的访问URL，可直接用于图片展示或后续表单提交。
