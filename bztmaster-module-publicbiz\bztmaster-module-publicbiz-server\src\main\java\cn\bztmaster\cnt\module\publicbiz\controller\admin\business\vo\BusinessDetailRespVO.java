package cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Data
@Schema(description = "商机中心 - 商机详情 Response VO")
public class BusinessDetailRespVO {
    @Schema(description = "商机基本信息")
    private BusinessRespVO business;

    @Schema(description = "商机跟进记录列表")
    private List<BusinessFollowupRespVO> followups;
} 