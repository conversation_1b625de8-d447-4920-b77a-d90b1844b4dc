package cn.bztmaster.cnt.module.publicbiz.controller.admin.leads;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.LeadsAssignReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.LeadsSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.leads.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.leads.LeadsConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadInfoDO;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.CreateMethodEnum;
import cn.bztmaster.cnt.module.publicbiz.service.leads.LeadsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 线索管理
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 线索管理")
@RestController
@RequestMapping("/publicbiz/leads")
@Validated
public class LeadsController {

    @Resource
    private LeadsService leadsService;

    @PostMapping("/create")
    @Operation(summary = "创建线索")
    @PreAuthorize("@ss.hasPermission('publicbiz:leads:create')")
    public CommonResult<Long> createLead(@Valid @RequestBody LeadsSaveReqVO createReqVO) {
        createReqVO.setCreateMethod(CreateMethodEnum.MANUAL_CREATION.getType());
        LeadsSaveReqDTO createReqDTO = LeadsConvert.INSTANCE.convert(createReqVO);
        Long id = leadsService.createLead(createReqDTO);
        return success(id);
    }

    @PutMapping("/update")
    @Operation(summary = "更新线索")
    @PreAuthorize("@ss.hasPermission('publicbiz:leads:update')")
    public CommonResult<Boolean> updateLead(@Valid @RequestBody LeadsSaveReqVO updateReqVO) {
        LeadsSaveReqDTO updateReqDTO = LeadsConvert.INSTANCE.convert(updateReqVO);
        leadsService.updateLead(updateReqDTO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除线索")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('publicbiz:leads:delete')")
    public CommonResult<Boolean> deleteLead(@RequestParam("id") Long id) {
        leadsService.deleteLead(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得线索详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('publicbiz:leads:query')")
    public CommonResult<LeadsRespVO> getLead(@RequestParam("id") Long id) {
        LeadInfoDO lead = leadsService.getLead(id);
        return success(LeadsConvert.INSTANCE.convert(lead));
    }

    @GetMapping("/get-by-lead-id")
    @Operation(summary = "根据线索ID获得线索详情")
    @Parameter(name = "leadId", description = "线索ID", required = true, example = "LEAD20250721001")
    @PreAuthorize("@ss.hasPermission('publicbiz:leads:query')")
    public CommonResult<LeadsRespVO> getLeadByLeadId(@RequestParam("leadId") String leadId) {
        LeadInfoDO lead = leadsService.getLeadByLeadId(leadId);
        return success(LeadsConvert.INSTANCE.convert(lead));
    }

    @GetMapping("/page")
    @Operation(summary = "获得线索分页")
    @PreAuthorize("@ss.hasPermission('publicbiz:leads:query')")
    public CommonResult<PageResult<LeadsRespVO>> getLeadPage(LeadsPageReqVO pageVO) {
        PageResult<LeadInfoDO> pageResult = leadsService.getLeadPage(LeadsConvert.INSTANCE.convert(pageVO));
        return success(LeadsConvert.INSTANCE.convertPage(pageResult));
    }

    @PostMapping("/assign")
    @Operation(summary = "分配线索")
    @PreAuthorize("@ss.hasPermission('publicbiz:leads:assign')")
    public CommonResult<Boolean> assignLead(@Valid @RequestBody LeadAssignReqVO assignReqVO) {
        LeadsAssignReqDTO assignReqDTO = LeadsConvert.INSTANCE.convert(assignReqVO);
        leadsService.assignLead(assignReqDTO);
        return success(true);
    }
}