package cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Schema(description = "师资库 - 新增讲师 Request VO")
public class TeacherSaveReqVO {
    @Schema(description = "头像URL")
    private String avatar;
    @Schema(description = "讲师姓名")
    private String name;
    @Schema(description = "简介")
    private String description;
    @Schema(description = "讲师类型")
    private String type;
    @Schema(description = "业务模块")
    private String biz;
    @Schema(description = "关联机构")
    private String org;
    @Schema(description = "擅长领域")
    private String field;
    @Schema(description = "联系电话")
    private String phone;
    @Schema(description = "邮箱地址")
    private String email;
    @Schema(description = "合作状态")
    private String status;
    @Schema(description = "电子签约状态")
    private String signStatus;
    @Schema(description = "签约日期")
    private Date signDate;
    @Schema(description = "合同类型")
    private String contractType;
    @Schema(description = "合同模板")
    private String contractTemplate;
    @Schema(description = "合同编号")
    private String contractNo;
    @Schema(description = "合同名称")
    private String contractName;
    @Schema(description = "合同周期开始")
    private Date contractPeriodStart;
    @Schema(description = "合同周期结束")
    private Date contractPeriodEnd;
    @Schema(description = "合同金额")
    private BigDecimal contractAmount;
    @Schema(description = "纸质合同附件文件名")
    private String contractFileName;
    @Schema(description = "纸质合同附件文件URL")
    private String contractFileUrl;
    @Schema(description = "讲师资质文件列表")
    private List<TeacherCertSaveReqVO> certFiles;
} 