package cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 课程统计概览 - Response VO
 * 对应前端页面：src/views/infra/ResourceCenter/DigitalAsset/DigitalAsset.vue
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "课程统计概览 - Response VO")
public class CourseStatisticsRespVO {

    @Schema(description = "课程总数", example = "25")
    private Integer totalCount;

    @Schema(description = "线上课程数", example = "15")
    private Integer onlineCount;

    @Schema(description = "线下课程数", example = "10")
    private Integer offlineCount;

    @Schema(description = "已上架课程数", example = "18")
    private Integer publishedCount;
}
