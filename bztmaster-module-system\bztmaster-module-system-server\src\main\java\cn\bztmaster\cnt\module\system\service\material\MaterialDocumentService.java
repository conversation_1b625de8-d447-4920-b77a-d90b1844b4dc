package cn.bztmaster.cnt.module.system.service.material;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;

public interface MaterialDocumentService {
    PageResult<DocumentRespVO> getDocumentPage(DocumentPageReqVO reqVO);
    Long createDocument(DocumentSaveReqVO reqVO);
    void updateDocument(DocumentSaveReqVO reqVO);
    void deleteDocument(Long id);
    DocumentRespVO getDocumentDetail(Long id);
    PageResult<DocumentRespVO> getDocumentRecyclePage(DocumentPageReqVO reqVO, String updateTimeFrom, String updateTimeTo);
    void restoreDocumentFromRecycle(java.util.List<Long> idList);
    void deleteDocumentFromRecycle(java.util.List<Long> idList);
} 