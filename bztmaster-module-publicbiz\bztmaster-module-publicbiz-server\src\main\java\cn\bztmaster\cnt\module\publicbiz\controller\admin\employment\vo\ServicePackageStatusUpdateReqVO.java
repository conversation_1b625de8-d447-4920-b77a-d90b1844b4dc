package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@Schema(description = "就业服务-服务套餐状态更新 Request VO")
public class ServicePackageStatusUpdateReqVO {
    @Schema(description = "套餐ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "套餐ID列表不能为空")
    private List<Long> ids;

    @Schema(description = "目标状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "目标状态不能为空")
    private String status;
} 