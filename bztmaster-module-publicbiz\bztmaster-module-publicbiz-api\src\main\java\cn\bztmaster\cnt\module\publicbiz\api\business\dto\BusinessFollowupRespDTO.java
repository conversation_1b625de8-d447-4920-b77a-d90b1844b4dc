package cn.bztmaster.cnt.module.publicbiz.api.business.dto;

import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@Schema(description = "商机中心 - 商机跟进 Response DTO")
public class BusinessFollowupRespDTO implements VO {
    @Schema(description = "跟进ID", example = "1")
    private Long id;

    @Schema(description = "商机ID", example = "1001")
    private Long businessId;

    @Schema(description = "跟进内容", example = "电话沟通")
    private String content;

    @Schema(description = "跟进时间")
    private Date followupTime;

    @Schema(description = "创建人ID")
    private Long creatorId;

    @Schema(description = "创建人名称")
    private String creatorName;

    @Schema(description = "创建时间")
    private Date createTime;
} 