<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.bztmaster.cnt</groupId>
        <artifactId>bztmaster-module-system</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>bztmaster-module-system-server</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        system 模块下，我们放通用业务，支撑上层的核心业务。
        例如说：用户、部门、权限、数据字典等等
    </description>

    <dependencies>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-env</artifactId>
        </dependency>

        <!-- 依赖服务 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-biz-ip</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-rpc</artifactId>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- 服务保障相关 TODO 芋艿：暂时去掉 -->
<!--        <dependency>-->
<!--            <groupId>cn.bztmaster.cnt</groupId>-->
<!--            <artifactId>bztmaster-spring-boot-starter-protection</artifactId>-->
<!--        </dependency>-->

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-monitor</artifactId>
        </dependency>

        <!-- 三方云服务相关 -->
        <dependency>
            <groupId>me.zhyd.oauth</groupId>
            <artifactId>JustAuth</artifactId> <!-- 社交登陆（例如说，个人微信、企业微信等等） -->
        </dependency>
        <dependency>
            <groupId>com.xkcoding.justauth</groupId>
            <artifactId>justauth-spring-boot-starter</artifactId>
            <exclusions>
                <!-- 移除，避免和项目里的 hutool-all 冲突 -->
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-mp-spring-boot-starter</artifactId> <!-- 微信登录（公众号） -->
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>  <!-- 微信登录（小程序） -->
        </dependency>

        <dependency>
            <groupId>com.anji-plus</groupId>
            <artifactId>captcha-spring-boot-starter</artifactId> <!-- 验证码，一般用于登录使用 -->
        </dependency>

    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
