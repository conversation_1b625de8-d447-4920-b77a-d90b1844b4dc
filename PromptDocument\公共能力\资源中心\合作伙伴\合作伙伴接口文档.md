
# 1. 合作伙伴模块（publicbiz_partner）

## 1.1 新增合作伙伴
- **接口地址**：/publicbiz/partner/create
- **请求方式**：POST
- **入参**：

| 字段名                    | 类型              | 说明                 |
|--------------------------|-------------------|----------------------|
| name                     | String            | 机构名称             |
| shortName                | String            | 机构简称             |
| type                     | String            | 机构类型             |
| biz                      | String            | 业务模块             |
| status                   | String            | 合作状态             |
| risk                     | String            | 风险等级             |
| owner                    | Long              | 我方负责人ID         |
| legalPerson              | String            | 法人代表             |
| foundationDate           | String            | 成立日期（yyyy-MM-dd或ISO）|
| creditCode               | String            | 统一社会信用代码     |
| registerAddress          | String            | 注册地址             |
| businessAddress          | String            | 经营地址             |
| mainBusiness             | String            | 主营业务             |
| contactName              | String            | 主要联系人           |
| contactPhone             | String            | 联系电话             |
| rating                   | Integer           | 当前评级（星级）     |
| cooperationMode          | String            | 合作模式             |
| contractNo               | String            | 合同编号             |
| contractStart            | String            | 合同开始日期（yyyy-MM-dd或ISO）|
| contractEnd              | String            | 合同结束日期（yyyy-MM-dd或ISO）|
| deposit                  | BigDecimal        | 保证金               |
| renewDate                | String            | 续约提醒日期（yyyy-MM-dd或ISO）|
| accountName              | String            | 对公账户名           |
| settlementCycle          | String            | 结算周期             |
| bankName                 | String            | 开户银行             |
| bankAccount              | String            | 银行账号             |
| qualificationFile        | String            | 资质文件（URL或ID）  |
| invoiceType              | String            | 开票类型             |
| invoiceName              | String            | 开票名称             |
| taxId                    | String            | 纳税人识别号         |
| orgCode                  | String            | 社会组织代码         |
| invoiceAddress           | String            | 开票地址             |
| invoicePhone             | String            | 开票电话             |
| invoiceBank              | String            | 开票开户银行         |
| invoiceBankAccount       | String            | 开票银行账号         |
| invoiceEmail             | String            | 开票邮箱             |
| invoiceContact           | String            | 开票联系人           |
| invoiceQualificationFile | String            | 开票资质文件（URL或ID）|
| invoiceRemark            | String            | 开票备注             |
| creator                  | String            | 创建者（可选）       |
| updater                  | String            | 更新者（可选）       |
| deleted                  | Boolean           | 是否删除（可选）     |
| tenantId                 | Long              | 租户编号（可选）     |

- **字段说明**：
  - 日期字段格式：YYYY-MM-DD
  - 金额字段为数字
  - 文件字段建议传文件URL或唯一ID
  - owner 未选择时传 0
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1
  }
}
```
- **请求示例**：
```
curl -X POST http://host/publicbiz/partner/create -H "Content-Type: application/json" -d '{
  "name": "北京大学",
  "shortName": "北大",
  "type": "高校",
  "biz": "合作中",
  "status": "正常",
  "risk": "低级",
  "owner": 0,
  "legalPerson": "李四",
  "foundationDate": "2023-01-01",
  "creditCode": "91110108MA01XXXXXX",
  "registerAddress": "北京市海淀区颐和园路5号",
  "businessAddress": "北京市海淀区颐和园路5号",
  "mainBusiness": "高等教育",
  "contactName": "王五",
  "contactPhone": "***********",
  "rating": 5,
  "cooperationMode": "项目合作",
  "contractNo": "HT20230101",
  "contractStart": "2023-01-01",
  "contractEnd": "2023-12-31",
  "deposit": 10000.00,
  "renewDate": "2023-12-01",
  "accountName": "北京大学",
  "settlementCycle": "月结",
  "bankName": "中国银行",
  "bankAccount": "6222021001111111111",
  "qualificationFile": "http://file-server/xxx.pdf",
  "invoiceType": "企业",
  "invoiceName": "北京大学",
  "taxId": "91110108MA01XXXXXX",
  "orgCode": "*********",
  "invoiceAddress": "北京市海淀区颐和园路5号",
  "invoicePhone": "010-6275XXXX",
  "invoiceBank": "中国银行",
  "invoiceBankAccount": "6222021001111111111",
  "invoiceEmail": "<EMAIL>",
  "invoiceContact": "赵六",
  "invoiceQualificationFile": "http://file-server/invoice.pdf",
  "invoiceRemark": "请提前开票",
  "creator": "admin",
  "updater": "admin",
  "deleted": false,
  "tenantId": 10001
}'
```

## 1.2 编辑合作伙伴

- **接口地址**：/publicbiz/partner/update  
- **请求方式**：POST  
- **入参**：

| 字段名                    | 类型              | 说明                 |
|--------------------------|-------------------|----------------------|
| id                       | Long              | 主键ID（必填）       |
| name                     | String            | 机构名称             |
| shortName                | String            | 机构简称             |
| type                     | String            | 机构类型             |
| biz                      | String            | 业务模块             |
| status                   | String            | 合作状态             |
| risk                     | String            | 风险等级             |
| owner                    | Long              | 我方负责人ID         |
| ownerName                | String            | 我方负责人昵称       |
| legalPerson              | String            | 法人代表             |
| foundationDate           | String            | 成立日期（yyyy-MM-dd或ISO）|
| creditCode               | String            | 统一社会信用代码     |
| registerAddress          | String            | 注册地址             |
| businessAddress          | String            | 经营地址             |
| mainBusiness             | String            | 主营业务             |
| contactName              | String            | 主要联系人           |
| contactPhone             | String            | 联系电话             |
| rating                   | Integer           | 当前评级（星级）     |
| cooperationMode          | String            | 合作模式             |
| contractNo               | String            | 合同编号             |
| contractStart            | String            | 合同开始日期（yyyy-MM-dd或ISO）|
| contractEnd              | String            | 合同结束日期（yyyy-MM-dd或ISO）|
| deposit                  | BigDecimal        | 保证金               |
| renewDate                | String            | 续约提醒日期（yyyy-MM-dd或ISO）|
| accountName              | String            | 对公账户名           |
| settlementCycle          | String            | 结算周期             |
| bankName                 | String            | 开户银行             |
| bankAccount              | String            | 银行账号             |
| qualificationFile        | String            | 资质文件（URL或ID）  |
| invoiceType              | String            | 开票类型             |
| invoiceName              | String            | 开票名称             |
| taxId                    | String            | 纳税人识别号         |
| orgCode                  | String            | 社会组织代码         |
| invoiceAddress           | String            | 开票地址             |
| invoicePhone             | String            | 开票电话             |
| invoiceBank              | String            | 开票开户银行         |
| invoiceBankAccount       | String            | 开票银行账号         |
| invoiceEmail             | String            | 开票邮箱             |
| invoiceContact           | String            | 开票联系人           |
| invoiceQualificationFile | String            | 开票资质文件（URL或ID）|
| invoiceRemark            | String            | 开票备注             |
| creator                  | String            | 创建者（可选）       |
| updater                  | String            | 更新者（可选）       |
| deleted                  | Boolean           | 是否删除（可选）     |
| tenantId                 | Long              | 租户编号（可选）     |

- **字段说明**：
  - 日期字段格式：YYYY-MM-DD
  - 金额字段为数字
  - 文件字段建议传文件URL或唯一ID
  - owner 未选择时传 0

- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": true
}
```

- **请求示例**：
```json
POST /publicbiz/partner/update
Content-Type: application/json

{
  "id": 1,
  "name": "北京大学",
  "shortName": "北大",
  "type": "高校",
  "biz": "合作中",
  "status": "正常",
  "risk": "低级",
  "owner": 1,
  "ownerName": "张三",
  "legalPerson": "李四",
  "foundationDate": "2023-01-01",
  "creditCode": "91110108MA01XXXXXX",
  "registerAddress": "北京市海淀区颐和园路5号",
  "businessAddress": "北京市海淀区颐和园路5号",
  "mainBusiness": "高等教育",
  "contactName": "王五",
  "contactPhone": "***********",
  "rating": 5,
  "cooperationMode": "项目合作",
  "contractNo": "HT20230101",
  "contractStart": "2023-01-01",
  "contractEnd": "2023-12-31",
  "deposit": 10000.00,
  "renewDate": "2023-12-01",
  "accountName": "北京大学",
  "settlementCycle": "月结",
  "bankName": "中国银行",
  "bankAccount": "6222021001111111111",
  "qualificationFile": "http://file-server/xxx.pdf",
  "invoiceType": "企业",
  "invoiceName": "北京大学",
  "taxId": "91110108MA01XXXXXX",
  "orgCode": "*********",
  "invoiceAddress": "北京市海淀区颐和园路5号",
  "invoicePhone": "010-6275XXXX",
  "invoiceBank": "中国银行",
  "invoiceBankAccount": "6222021001111111111",
  "invoiceEmail": "<EMAIL>",
  "invoiceContact": "赵六",
  "invoiceQualificationFile": "http://file-server/invoice.pdf",
  "invoiceRemark": "请提前开票",
  "creator": "admin",
  "updater": "admin",
  "deleted": false,
  "tenantId": 10001
}
```

## 1.3 删除合作伙伴
- **接口地址**：/publicbiz/partner/delete
- **请求方式**：POST
- **入参**：
```
{
  "id": 1
}
```
- **出参**：
```
{
  "code": 0,
  "msg": "success"
}
```

## 1.4 合作伙伴详情
- **接口地址**：/publicbiz/partner/detail
- **请求方式**：GET
- **入参**：
```
?id=1
```
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "name": "北京大学",
    "shortName": "北大",
    "type": "高校",
    "biz": "合作中",
    "status": "正常",
    "risk": "低级",
    "owner": 1,
    "ownerName": "张三",
    "legalPerson": "李四",
    "foundationDate": "2023-01-01",
    "creditCode": "91110108MA01XXXXXX",
    "registerAddress": "北京市海淀区颐和园路5号",
    "businessAddress": "北京市海淀区颐和园路5号",
    "mainBusiness": "高等教育",
    "contactName": "王五",
    "contactPhone": "***********",
    "rating": 5,
    "cooperationMode": "项目合作",
    "contractNo": "HT20230101",
    "contractStart": "2023-01-01",
    "contractEnd": "2023-12-31",
    "deposit": 10000.00,
    "renewDate": "2023-12-01",
    "accountName": "北京大学",
    "settlementCycle": "月结",
    "bankName": "中国银行",
    "bankAccount": "6222021001111111111",
    "qualificationFile": "http://file-server/xxx.pdf",
    "invoiceType": "企业",
    "invoiceName": "北京大学",
    "taxId": "91110108MA01XXXXXX",
    "orgCode": "*********",
    "invoiceAddress": "北京市海淀区颐和园路5号",
    "invoicePhone": "010-6275XXXX",
    "invoiceBank": "中国银行",
    "invoiceBankAccount": "6222021001111111111",
    "invoiceEmail": "<EMAIL>",
    "invoiceContact": "赵六",
    "invoiceQualificationFile": "http://file-server/invoice.pdf",
    "invoiceRemark": "请提前开票",
    "creator": "admin",
    "updater": "admin",
    "deleted": false,
    "tenantId": 10001,
    "createTime": "2023-01-15 10:00:00",
    "updateTime": "2023-01-15 10:00:00"
  }
}
```
- **字段说明**：
  - 字段与新增/编辑接口一致，详见新增接口文档。
  - 文件、日期、金额等类型同新增接口。

## 1.5 合作伙伴分页查询
- **接口地址**：/publicbiz/partner/page
- **请求方式**：GET
- **入参**：
```
?page=1&size=10&type=高校&biz=合作中&status=正常&keyword=北京
```
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 2,
    "list": [
      {
        "id": 1,
        "name": "北京大学",
        "type": "高校",
        "biz": "合作中",
        "status": "正常",
        "risk": "低级",
        "owner": "张三",
        "create_time": "2023-01-15 10:00:00",
        "update_time": "2023-01-15 10:00:00"
      }
    ]
  }
}
```

# 1.6 合作伙伴统计卡片数据
- **接口地址**：/publicbiz/partner/stat
- **请求方式**：GET
- **入参**：无
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": {
    "partnerTotal": 5,
    "partnerActive": 3,
    "partnerPending": 1,
    "partnerRisk": 1
  }
}
```
- **请求示例**：
```
curl http://host/publicbiz/partner/stat
```
## 1.9 获取有效状态合作伙伴列表（下拉框数据）
- **接口地址**：/publicbiz/partner/list/active
- **请求方式**：GET
- **接口描述**：获取所有有效状态的合作伙伴数据，用于前端下拉框、选择器等组件的数据绑定
- **权限要求**：无特殊权限要求

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| status | String | 否 | 合作状态筛选，多个状态用逗号分隔 | "正常,待审核" |
| type | String | 否 | 机构类型筛选 | "高校" |
| biz | String | 否 | 业务模块筛选 | "合作中" |
| keyword | String | 否 | 机构名称关键词搜索 | "北京" |

#### 响应参数

| 参数名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| list | Array | 合作伙伴数据列表 | - |
| list[].id | Long | 合作伙伴ID | 1 |
| list[].name | String | 机构全称 | "北京大学" |
| list[].shortName | String | 机构简称 | "北大" |
| list[].type | String | 机构类型 | "高校" |
| list[].biz | String | 业务模块 | "合作中" |
| list[].status | String | 合作状态 | "正常" |
| list[].contactName | String | 主要联系人 | "王五" |
| list[].contactPhone | String | 联系电话 | "***********" |

#### 请求示例

```bash
# 获取所有有效状态的合作伙伴
GET /publicbiz/partner/list/active

# 按状态筛选
GET /publicbiz/partner/list/active?status=正常,待审核

# 按机构类型筛选
GET /publicbiz/partner/list/active?type=高校

# 按业务模块筛选
GET /publicbiz/partner/list/active?biz=合作中

# 按关键词搜索
GET /publicbiz/partner/list/active?keyword=北京

# 组合查询
GET /publicbiz/partner/list/active?status=正常&type=高校&keyword=北京
```

#### 响应示例

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "北京大学",
        "shortName": "北大",
        "type": "高校",
        "biz": "合作中",
        "status": "正常",
        "contactName": "王五",
        "contactPhone": "***********"
      },
      {
        "id": 2,
        "name": "清华大学",
        "shortName": "清华",
        "type": "高校",
        "biz": "合作中",
        "status": "正常",
        "contactName": "李老师",
        "contactPhone": "13900000000"
      },
      {
        "id": 3,
        "name": "北京理工大学",
        "shortName": "北理工",
        "type": "高校",
        "biz": "待审核",
        "status": "待审核",
        "contactName": "张老师",
        "contactPhone": "13700000000"
      }
    ]
  }
}
```

#### 使用场景

1. **下拉框数据绑定**：前端选择合作伙伴时，加载有效状态的合作伙伴列表
2. **自动完成组件**：搜索合作伙伴时，提供候选数据
3. **关联选择**：在其他业务模块中需要选择合作伙伴时使用
4. **数据展示**：在报表、统计等场景中展示合作伙伴基础信息

#### 字段说明

- **id**：合作伙伴唯一标识，用于表单提交、关联查询等
- **name**：机构全称，用于显示和搜索
- **shortName**：机构简称，用于紧凑显示
- **type**：机构类型，用于分类展示
- **biz**：业务模块，用于业务筛选
- **status**：合作状态，用于状态标识
- **contactName**：主要联系人，用于联系信息展示
- **contactPhone**：联系电话，用于联系信息展示

#### 注意事项

1. 该接口只返回有效状态（非删除状态）的合作伙伴数据
2. 返回数据按机构名称排序，便于用户查找
3. 支持多条件组合查询，提高查询效率
4. 返回字段精简，只包含下拉框展示必需的信息
5. 建议前端对返回数据进行缓存，减少重复请求