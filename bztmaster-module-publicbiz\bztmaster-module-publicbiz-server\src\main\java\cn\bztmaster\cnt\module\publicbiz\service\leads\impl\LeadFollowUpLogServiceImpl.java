package cn.bztmaster.cnt.module.publicbiz.service.leads.impl;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.LeadFollowUpLogPageReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.LeadFollowUpLogSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.leads.vo.LeadFollowUpLogPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadFollowUpLogDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadInfoDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.leads.LeadFollowUpLogMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.leads.LeadInfoMapper;
import cn.bztmaster.cnt.module.publicbiz.service.leads.LeadFollowUpLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.*;

/**
 * 线索跟进记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class LeadFollowUpLogServiceImpl implements LeadFollowUpLogService {

    @Resource
    private LeadFollowUpLogMapper leadFollowUpLogMapper;

    @Resource
    private LeadInfoMapper leadInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createLeadFollowUpLog(LeadFollowUpLogSaveReqDTO createReqDTO) {
        // 1. 校验线索是否存在
        validateLeadExists(createReqDTO.getLeadId());

        // 2. 插入
        LeadFollowUpLogDO leadFollowUpLog = new LeadFollowUpLogDO();
        leadFollowUpLog.setLeadId(createReqDTO.getLeadId());
        leadFollowUpLog.setFollowUpContent(createReqDTO.getFollowUpContent());

        // 设置创建人姓名
        String currentUserNickname = SecurityFrameworkUtils.getLoginUserNickname();
        if (currentUserNickname != null) {
            leadFollowUpLog.setCreatorName(currentUserNickname);
        }

        leadFollowUpLogMapper.insert(leadFollowUpLog);

        // 3. 返回
        return leadFollowUpLog.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLeadFollowUpLog(LeadFollowUpLogSaveReqDTO updateReqDTO) {
        // 1. 校验存在
        validateLeadFollowUpLogExists(updateReqDTO.getId());
        // 2. 校验线索是否存在
        validateLeadExists(updateReqDTO.getLeadId());

        // 3. 更新
        LeadFollowUpLogDO updateObj = new LeadFollowUpLogDO();
        updateObj.setId(updateReqDTO.getId());
        updateObj.setLeadId(updateReqDTO.getLeadId());
        updateObj.setFollowUpContent(updateReqDTO.getFollowUpContent());
        leadFollowUpLogMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLeadFollowUpLog(Long id) {
        // 校验存在
        validateLeadFollowUpLogExists(id);
        // 删除
        leadFollowUpLogMapper.deleteById(id);
    }

    @Override
    public LeadFollowUpLogDO getLeadFollowUpLog(Long id) {
        return leadFollowUpLogMapper.selectById(id);
    }

    @Override
    public List<LeadFollowUpLogDO> getLeadFollowUpLogListByLeadId(String leadId) {
        return leadFollowUpLogMapper.selectListByLeadId(leadId);
    }

    @Override
    public PageResult<LeadFollowUpLogDO> getLeadFollowUpLogPage(LeadFollowUpLogPageReqDTO pageReqDTO) {
        // 将 DTO 转换为 VO
        LeadFollowUpLogPageReqVO reqVO = new LeadFollowUpLogPageReqVO();
        reqVO.setPageNo(pageReqDTO.getPageNo());
        reqVO.setPageSize(pageReqDTO.getPageSize());
        reqVO.setLeadId(pageReqDTO.getLeadId());
        reqVO.setBeginCreateTime(pageReqDTO.getBeginCreateTime());
        reqVO.setEndCreateTime(pageReqDTO.getEndCreateTime());

        return leadFollowUpLogMapper.selectPage(reqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLeadFollowUpLogByLeadId(String leadId) {
        leadFollowUpLogMapper.deleteByLeadId(leadId);
    }

    /**
     * 校验线索跟进记录是否存在
     *
     * @param id 线索跟进记录编号
     */
    private void validateLeadFollowUpLogExists(Long id) {
        if (id == null) {
            return;
        }
        LeadFollowUpLogDO leadFollowUpLog = leadFollowUpLogMapper.selectById(id);
        if (leadFollowUpLog == null) {
            throw exception(LEAD_FOLLOW_UP_LOG_NOT_EXISTS);
        }
    }

    /**
     * 校验线索是否存在
     *
     * @param leadId 线索ID
     */
    private void validateLeadExists(String leadId) {
        LeadInfoDO leadInfo = leadInfoMapper.selectByLeadId(leadId);
        if (leadInfo == null) {
            throw exception(LEAD_NOT_EXISTS);
        }
    }
}