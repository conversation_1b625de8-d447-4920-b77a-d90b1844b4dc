package cn.bztmaster.cnt.module.publicbiz.convert.business;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.business.BusinessFollowupDO;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper(componentModel = "spring")
public interface BusinessFollowupConvert {
    BusinessFollowupConvert INSTANCE = Mappers.getMapper(BusinessFollowupConvert.class);

    BusinessFollowupDO convert(BusinessFollowupSaveReqVO bean);
    BusinessFollowupRespVO convert(BusinessFollowupDO bean);
    List<BusinessFollowupRespVO> convertList(List<BusinessFollowupDO> list);
    PageResult<BusinessFollowupRespVO> convertPage(PageResult<BusinessFollowupDO> page);
} 