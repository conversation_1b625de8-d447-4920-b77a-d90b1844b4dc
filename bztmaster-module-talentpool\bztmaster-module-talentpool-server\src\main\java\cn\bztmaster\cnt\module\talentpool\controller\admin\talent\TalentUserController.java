package cn.bztmaster.cnt.module.talentpool.controller.admin.talent;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserPageReqDTO;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserRespDTO;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserUpdateReqDTO;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserCreateReqDTO;
import cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentUserDO;
import cn.bztmaster.cnt.module.talentpool.service.talent.TalentUserService;
import cn.hutool.core.collection.CollUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;
import static cn.bztmaster.cnt.framework.common.util.collection.CollectionUtils.convertList;

/**
 * 人才用户 Controller
 *
 * 提供人才库用户的分页、详情、编辑、停用、合并等接口。
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apachecore/talentpool")
@Tag(name = "人才用户")
public class TalentUserController {

    @Resource
    private TalentUserService talentUserService;

    @GetMapping("/page")
    @Operation(summary = "分页查询人才库用户列表")
    public CommonResult<PageResult<TalentUserRespDTO>> getTalentUserPage(TalentUserPageReqDTO reqDTO) {
        PageResult<TalentUserRespDTO> pageResult = talentUserService.getTalentUserPage(reqDTO);
        return CommonResult.success(pageResult);
    }

    @GetMapping("/get")
    @Operation(summary = "根据ID获取人才详细信息")
    public CommonResult<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO> getTalentUser(
            @RequestParam("id") Long id) {
        return CommonResult.success(talentUserService.getTalentUserDetail(id));
    }

    @PutMapping("/update")
    @Operation(summary = "编辑人才基本信息")
    public CommonResult<Boolean> updateTalentUser(@RequestBody TalentUserUpdateReqDTO reqDTO) {
        talentUserService.updateTalentUser(reqDTO);
        return CommonResult.success(true);
    }

    @PostMapping("/disable")
    @Operation(summary = "停用人才")
    public CommonResult<Boolean> disableTalentUser(@RequestParam("id") Long id) {
        talentUserService.disableTalentUser(id);
        return CommonResult.success(true);
    }

    @PostMapping("/merge")
    @Operation(summary = "合并人才（多级识别：身份证号/手机号/UnionID/人工辅助）")
    public CommonResult<Boolean> mergeTalentUser(@RequestParam("id") Long id) {
        talentUserService.mergeTalentUser(id);
        return CommonResult.success(true);
    }

    @PostMapping("/create")
    @Operation(summary = "新增人才档案")
    public CommonResult<Map<String, Object>> createTalentUser(@RequestBody TalentUserCreateReqDTO reqDTO) {
        // reqDTO 已支持 orgId、orgName、talentSource、isSelfSupport 字段
        Long id = talentUserService.createTalentUser(reqDTO);
        return CommonResult.success(java.util.Collections.singletonMap("id", id));
    }

    // /get 和 /detail 接口返回的DTO已包含新字段，无需Controller层特殊处理
    @GetMapping("/detail")
    @Operation(summary = "获取人才画像详情")
    public CommonResult<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO> getTalentUserDetail(
            @RequestParam("id") Long id) {
        return CommonResult.success(talentUserService.getTalentUserDetail(id));
    }

    @PostMapping("/change-status")
    @Operation(summary = "变更人才状态（启用/停用/禁用等）")
    public CommonResult<Boolean> changeTalentUserStatus(@RequestParam("id") Long id,
            @RequestParam("status") String status) {
        talentUserService.changeTalentUserStatus(id, status);
        return CommonResult.success(true);
    }
}
