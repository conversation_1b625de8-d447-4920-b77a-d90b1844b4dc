package cn.bztmaster.cnt.module.publicbiz.service.leads;

import cn.bztmaster.cnt.framework.common.exception.ServiceException;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.framework.test.core.ut.BaseDbUnitTest;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadInfoDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.leads.LeadInfoMapper;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.BusinessModuleEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.CreateMethodEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.LeadSourceEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.LeadStatusEnum;
import cn.bztmaster.cnt.module.publicbiz.service.leads.impl.LeadsServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * {@link LeadsServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
class LeadsServiceTest extends BaseDbUnitTest {

    @InjectMocks
    private LeadsServiceImpl leadsService;

    @Mock
    private LeadInfoMapper leadInfoMapper;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCreateLead_success() {
        // 准备参数
        LeadsSaveReqDTO reqDTO = new LeadsSaveReqDTO();
        reqDTO.setCustomerName("测试客户");
        reqDTO.setCustomerPhone("***********");
        reqDTO.setLeadSource(LeadSourceEnum.WEBSITE_REGISTRATION.getType());
        reqDTO.setBusinessModule(BusinessModuleEnum.UNIVERSITY_BUSINESS.getType());
        reqDTO.setLeadStatus(LeadStatusEnum.FOLLOWING_UP.getType());
        reqDTO.setCreateMethod(CreateMethodEnum.MANUAL_CREATION.getType());
        reqDTO.setRemark("测试备注");

        // mock 方法
        when(leadInfoMapper.insert(any(LeadInfoDO.class))).thenAnswer(invocation -> {
            LeadInfoDO leadInfoDO = invocation.getArgument(0);
            leadInfoDO.setId(1L);
            return 1;
        });

        // 调用
        Long leadId = leadsService.createLead(reqDTO);

        // 断言
        assertEquals(1L, leadId);
        
        // 验证 leadInfoMapper 的调用
        ArgumentCaptor<LeadInfoDO> leadInfoCaptor = ArgumentCaptor.forClass(LeadInfoDO.class);
        verify(leadInfoMapper).insert(leadInfoCaptor.capture());
        
        LeadInfoDO capturedLeadInfo = leadInfoCaptor.getValue();
        assertEquals(reqDTO.getCustomerName(), capturedLeadInfo.getCustomerName());
        assertEquals(reqDTO.getCustomerPhone(), capturedLeadInfo.getCustomerPhone());
        assertEquals(reqDTO.getLeadSource(), capturedLeadInfo.getLeadSource());
        assertEquals(reqDTO.getBusinessModule(), capturedLeadInfo.getBusinessModule());
        assertEquals(reqDTO.getLeadStatus(), capturedLeadInfo.getLeadStatus());
        assertEquals(reqDTO.getCreateMethod(), capturedLeadInfo.getCreateMethod());
        assertEquals(reqDTO.getRemark(), capturedLeadInfo.getRemark());
        assertNotNull(capturedLeadInfo.getLeadId());
        assertTrue(capturedLeadInfo.getLeadId().startsWith("XS"));
    }

    @Test
    void testCreateLead_withProvidedLeadId() {
        // 准备参数
        LeadsSaveReqDTO reqDTO = new LeadsSaveReqDTO();
        reqDTO.setLeadId("XS20250721001");
        reqDTO.setCustomerName("测试客户");
        reqDTO.setCustomerPhone("***********");
        reqDTO.setLeadSource(LeadSourceEnum.COLD_CALL.getType());
        reqDTO.setBusinessModule(BusinessModuleEnum.CERTIFICATION_BUSINESS.getType());
        reqDTO.setLeadStatus(LeadStatusEnum.CONVERTED.getType());
        reqDTO.setCreateMethod(CreateMethodEnum.MANUAL_CREATION.getType());

        // mock 方法
        when(leadInfoMapper.selectByLeadId(reqDTO.getLeadId())).thenReturn(null);
        when(leadInfoMapper.insert(any(LeadInfoDO.class))).thenAnswer(invocation -> {
            LeadInfoDO leadInfoDO = invocation.getArgument(0);
            leadInfoDO.setId(1L);
            return 1;
        });

        // 调用
        Long leadId = leadsService.createLead(reqDTO);

        // 断言
        assertEquals(1L, leadId);
        
        // 验证 leadInfoMapper 的调用
        ArgumentCaptor<LeadInfoDO> leadInfoCaptor = ArgumentCaptor.forClass(LeadInfoDO.class);
        verify(leadInfoMapper).insert(leadInfoCaptor.capture());
        
        LeadInfoDO capturedLeadInfo = leadInfoCaptor.getValue();
        assertEquals(reqDTO.getLeadId(), capturedLeadInfo.getLeadId());
    }

    @Test
    void testCreateLead_leadIdExists() {
        // 准备参数
        LeadsSaveReqDTO reqDTO = new LeadsSaveReqDTO();
        reqDTO.setLeadId("XS20250721001");
        reqDTO.setCustomerName("测试客户");
        reqDTO.setCustomerPhone("***********");

        // mock 方法 - 模拟线索ID已存在
        LeadInfoDO existingLead = new LeadInfoDO();
        existingLead.setId(1L);
        existingLead.setLeadId(reqDTO.getLeadId());
        when(leadInfoMapper.selectByLeadId(reqDTO.getLeadId())).thenReturn(existingLead);

        // 调用，并断言异常
        ServiceException exception = assertThrows(ServiceException.class, () -> leadsService.createLead(reqDTO));
        assertEquals(LEAD_ID_EXISTS.getCode(), exception.getCode());
    }

    @Test
    void testUpdateLead_success() {
        // 准备参数
        LeadsSaveReqDTO reqDTO = new LeadsSaveReqDTO();
        reqDTO.setId(1L);
        reqDTO.setLeadId("XS20250721001");
        reqDTO.setCustomerName("更新客户");
        reqDTO.setCustomerPhone("***********");
        reqDTO.setLeadSource(LeadSourceEnum.REFERRAL.getType());
        reqDTO.setBusinessModule(BusinessModuleEnum.UNIVERSITY_BUSINESS.getType());
        reqDTO.setLeadStatus(LeadStatusEnum.FOLLOWING_UP.getType());
        reqDTO.setCreateMethod(CreateMethodEnum.MANUAL_CREATION.getType());
        reqDTO.setRemark("更新备注");

        // mock 方法
        LeadInfoDO existingLead = new LeadInfoDO();
        existingLead.setId(1L);
        existingLead.setLeadId(reqDTO.getLeadId());
        when(leadInfoMapper.selectById(reqDTO.getId())).thenReturn(existingLead);
        when(leadInfoMapper.selectByLeadId(reqDTO.getLeadId())).thenReturn(existingLead);
        when(leadInfoMapper.updateById(any(LeadInfoDO.class))).thenReturn(1);

        // 调用
        leadsService.updateLead(reqDTO);

        // 验证 leadInfoMapper 的调用
        ArgumentCaptor<LeadInfoDO> leadInfoCaptor = ArgumentCaptor.forClass(LeadInfoDO.class);
        verify(leadInfoMapper).updateById(leadInfoCaptor.capture());
        
        LeadInfoDO capturedLeadInfo = leadInfoCaptor.getValue();
        assertEquals(reqDTO.getId(), capturedLeadInfo.getId());
        assertEquals(reqDTO.getCustomerName(), capturedLeadInfo.getCustomerName());
        assertEquals(reqDTO.getCustomerPhone(), capturedLeadInfo.getCustomerPhone());
        assertEquals(reqDTO.getLeadSource(), capturedLeadInfo.getLeadSource());
        assertEquals(reqDTO.getBusinessModule(), capturedLeadInfo.getBusinessModule());
        assertEquals(reqDTO.getLeadStatus(), capturedLeadInfo.getLeadStatus());
        assertEquals(reqDTO.getCreateMethod(), capturedLeadInfo.getCreateMethod());
        assertEquals(reqDTO.getRemark(), capturedLeadInfo.getRemark());
    }

    @Test
    void testUpdateLead_notExists() {
        // 准备参数
        LeadsSaveReqDTO reqDTO = new LeadsSaveReqDTO();
        reqDTO.setId(1L);
        reqDTO.setCustomerName("更新客户");

        // mock 方法 - 模拟线索不存在
        when(leadInfoMapper.selectById(reqDTO.getId())).thenReturn(null);

        // 调用，并断言异常
        ServiceException exception = assertThrows(ServiceException.class, () -> leadsService.updateLead(reqDTO));
        assertEquals(LEAD_NOT_EXISTS.getCode(), exception.getCode());
    }

    @Test
    void testDeleteLead_success() {
        // 准备参数
        Long id = 1L;

        // mock 方法
        LeadInfoDO existingLead = new LeadInfoDO();
        existingLead.setId(id);
        when(leadInfoMapper.selectById(id)).thenReturn(existingLead);
        when(leadInfoMapper.deleteById(id)).thenReturn(1);

        // 调用
        leadsService.deleteLead(id);

        // 验证 leadInfoMapper 的调用
        verify(leadInfoMapper).deleteById(id);
    }

    @Test
    void testDeleteLead_notExists() {
        // 准备参数
        Long id = 1L;

        // mock 方法 - 模拟线索不存在
        when(leadInfoMapper.selectById(id)).thenReturn(null);

        // 调用，并断言异常
        ServiceException exception = assertThrows(ServiceException.class, () -> leadsService.deleteLead(id));
        assertEquals(LEAD_NOT_EXISTS.getCode(), exception.getCode());
    }

    @Test
    void testGetLead() {
        // 准备参数
        Long id = 1L;

        // mock 方法
        LeadInfoDO lead = new LeadInfoDO();
        lead.setId(id);
        lead.setLeadId("XS20250721001");
        lead.setCustomerName("测试客户");
        when(leadInfoMapper.selectById(id)).thenReturn(lead);

        // 调用
        LeadInfoDO result = leadsService.getLead(id);

        // 断言
        assertNotNull(result);
        assertEquals(id, result.getId());
        assertEquals("XS20250721001", result.getLeadId());
        assertEquals("测试客户", result.getCustomerName());
    }

    @Test
    void testGetLeadByLeadId() {
        // 准备参数
        String leadId = "XS20250721001";

        // mock 方法
        LeadInfoDO lead = new LeadInfoDO();
        lead.setId(1L);
        lead.setLeadId(leadId);
        lead.setCustomerName("测试客户");
        when(leadInfoMapper.selectByLeadId(leadId)).thenReturn(lead);

        // 调用
        LeadInfoDO result = leadsService.getLeadByLeadId(leadId);

        // 断言
        assertNotNull(result);
        assertEquals(leadId, result.getLeadId());
        assertEquals("测试客户", result.getCustomerName());
    }

    @Test
    void testGetLeadList() {
        // 准备参数
        LeadsListReqDTO listReqDTO = new LeadsListReqDTO();
        listReqDTO.setCustomerName("测试");
        listReqDTO.setLeadStatus(LeadStatusEnum.UNPROCESSED.getType());

        // mock 方法
        List<LeadInfoDO> leadList = new ArrayList<>();
        LeadInfoDO lead1 = new LeadInfoDO();
        lead1.setId(1L);
        lead1.setLeadId("XS20250721001");
        lead1.setCustomerName("测试客户1");
        leadList.add(lead1);
        
        LeadInfoDO lead2 = new LeadInfoDO();
        lead2.setId(2L);
        lead2.setLeadId("XS20250721002");
        lead2.setCustomerName("测试客户2");
        leadList.add(lead2);
        
        when(leadInfoMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(leadList);

        // 调用
        List<LeadInfoDO> result = leadsService.getLeadList(listReqDTO);

        // 断言
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("测试客户1", result.get(0).getCustomerName());
        assertEquals("测试客户2", result.get(1).getCustomerName());
    }

    @Test
    void testGetLeadPage() {
        // 准备参数
        LeadsPageReqDTO pageReqDTO = new LeadsPageReqDTO();
        pageReqDTO.setCustomerName("测试");
        pageReqDTO.setLeadStatus(LeadStatusEnum.UNPROCESSED.getType());
        pageReqDTO.setPageNo(1);
        pageReqDTO.setPageSize(10);

        // mock 方法
        List<LeadInfoDO> leadList = new ArrayList<>();
        LeadInfoDO lead1 = new LeadInfoDO();
        lead1.setId(1L);
        lead1.setLeadId("XS20250721001");
        lead1.setCustomerName("测试客户1");
        leadList.add(lead1);
        
        PageResult<LeadInfoDO> pageResult = new PageResult<>();
        pageResult.setList(leadList);
        pageResult.setTotal(1L);
        
        when(leadInfoMapper.selectPage(eq(pageReqDTO), any(LambdaQueryWrapperX.class))).thenReturn(pageResult);

        // 调用
        PageResult<LeadInfoDO> result = leadsService.getLeadPage(pageReqDTO);

        // 断言
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getList().size());
        assertEquals("测试客户1", result.getList().get(0).getCustomerName());
    }

    @Test
    void testAssignLead_success() {
        // 准备参数
        LeadsAssignReqDTO assignReqDTO = new LeadsAssignReqDTO();
        assignReqDTO.setLeadId("XS20250721001");
        assignReqDTO.setUserName("销售员A");
        assignReqDTO.setAssignRemark("分配给销售员A处理");

        // mock 方法
        LeadInfoDO existingLead = new LeadInfoDO();
        existingLead.setId(1L);
        existingLead.setLeadId(assignReqDTO.getLeadId());
        existingLead.setLeadStatus(LeadStatusEnum.UNPROCESSED.getType());
        when(leadInfoMapper.selectByLeadId(assignReqDTO.getLeadId())).thenReturn(existingLead);
        when(leadInfoMapper.updateById(any(LeadInfoDO.class))).thenReturn(1);

        // 调用
        leadsService.assignLead(assignReqDTO);

        // 验证 leadInfoMapper 的调用
        ArgumentCaptor<LeadInfoDO> leadInfoCaptor = ArgumentCaptor.forClass(LeadInfoDO.class);
        verify(leadInfoMapper).updateById(leadInfoCaptor.capture());
        
        LeadInfoDO capturedLeadInfo = leadInfoCaptor.getValue();
        assertEquals(existingLead.getId(), capturedLeadInfo.getId());
        assertEquals(assignReqDTO.getUserName(), capturedLeadInfo.getCurrentOwner());
        assertEquals(LeadStatusEnum.FOLLOWING_UP.getType(), capturedLeadInfo.getLeadStatus());
    }

    @Test
    void testAssignLead_leadNotExists() {
        // 准备参数
        LeadsAssignReqDTO assignReqDTO = new LeadsAssignReqDTO();
        assignReqDTO.setLeadId("XS20250721001");
        assignReqDTO.setUserName("销售员A");

        // mock 方法 - 模拟线索不存在
        when(leadInfoMapper.selectByLeadId(assignReqDTO.getLeadId())).thenReturn(null);

        // 调用，并断言异常
        ServiceException exception = assertThrows(ServiceException.class, () -> leadsService.assignLead(assignReqDTO));
        assertEquals(LEAD_NOT_EXISTS.getCode(), exception.getCode());
    }

    @Test
    void testCreateLead_customerPhoneExists() {
        // 准备参数
        LeadsSaveReqDTO reqDTO = new LeadsSaveReqDTO();
        reqDTO.setCustomerName("测试客户");
        reqDTO.setCustomerPhone("***********");
        reqDTO.setLeadSource(LeadSourceEnum.WEBSITE_REGISTRATION.getType());
        reqDTO.setBusinessModule(BusinessModuleEnum.UNIVERSITY_BUSINESS.getType());
        reqDTO.setLeadStatus(LeadStatusEnum.FOLLOWING_UP.getType());
        reqDTO.setCreateMethod(CreateMethodEnum.MANUAL_CREATION.getType());

        // mock 方法 - 模拟客户联系电话已存在
        LeadInfoDO existingLead = new LeadInfoDO();
        existingLead.setId(1L);
        existingLead.setCustomerPhone("***********");
        List<LeadInfoDO> existingLeads = new ArrayList<>();
        existingLeads.add(existingLead);
        when(leadInfoMapper.selectListByCustomerPhone("***********")).thenReturn(existingLeads);

        // 调用，并断言异常
        ServiceException exception = assertThrows(ServiceException.class, () -> leadsService.createLead(reqDTO));
        assertEquals(LEAD_CUSTOMER_PHONE_EXISTS.getCode(), exception.getCode());
    }

    @Test
    void testUpdateLead_customerPhoneExists() {
        // 准备参数
        LeadsSaveReqDTO reqDTO = new LeadsSaveReqDTO();
        reqDTO.setId(1L);
        reqDTO.setCustomerName("更新客户");
        reqDTO.setCustomerPhone("***********");
        reqDTO.setLeadSource(LeadSourceEnum.WEBSITE_REGISTRATION.getType());
        reqDTO.setBusinessModule(BusinessModuleEnum.UNIVERSITY_BUSINESS.getType());
        reqDTO.setLeadStatus(LeadStatusEnum.FOLLOWING_UP.getType());
        reqDTO.setCreateMethod(CreateMethodEnum.MANUAL_CREATION.getType());

        // mock 方法 - 模拟当前线索存在
        LeadInfoDO currentLead = new LeadInfoDO();
        currentLead.setId(1L);
        currentLead.setCustomerPhone("***********");
        when(leadInfoMapper.selectById(1L)).thenReturn(currentLead);

        // mock 方法 - 模拟其他线索使用了相同的联系电话
        LeadInfoDO existingLead = new LeadInfoDO();
        existingLead.setId(2L);
        existingLead.setCustomerPhone("***********");
        List<LeadInfoDO> existingLeads = new ArrayList<>();
        existingLeads.add(existingLead);
        when(leadInfoMapper.selectListByCustomerPhone("***********")).thenReturn(existingLeads);

        // 调用，并断言异常
        ServiceException exception = assertThrows(ServiceException.class, () -> leadsService.updateLead(reqDTO));
        assertEquals(LEAD_CUSTOMER_PHONE_EXISTS.getCode(), exception.getCode());
    }

    @Test
    void testUpdateLead_customerPhoneNotChanged() {
        // 准备参数
        LeadsSaveReqDTO reqDTO = new LeadsSaveReqDTO();
        reqDTO.setId(1L);
        reqDTO.setCustomerName("更新客户");
        reqDTO.setCustomerPhone("***********");
        reqDTO.setLeadSource(LeadSourceEnum.WEBSITE_REGISTRATION.getType());
        reqDTO.setBusinessModule(BusinessModuleEnum.UNIVERSITY_BUSINESS.getType());
        reqDTO.setLeadStatus(LeadStatusEnum.FOLLOWING_UP.getType());
        reqDTO.setCreateMethod(CreateMethodEnum.MANUAL_CREATION.getType());

        // mock 方法 - 模拟当前线索存在
        LeadInfoDO currentLead = new LeadInfoDO();
        currentLead.setId(1L);
        currentLead.setCustomerPhone("***********");
        when(leadInfoMapper.selectById(1L)).thenReturn(currentLead);

        // mock 方法 - 模拟只有当前线索使用了该联系电话
        List<LeadInfoDO> existingLeads = new ArrayList<>();
        existingLeads.add(currentLead);
        when(leadInfoMapper.selectListByCustomerPhone("***********")).thenReturn(existingLeads);
        when(leadInfoMapper.updateById(any(LeadInfoDO.class))).thenReturn(1);

        // 调用 - 应该成功，不抛出异常
        assertDoesNotThrow(() -> leadsService.updateLead(reqDTO));

        // 验证 updateById 被调用
        verify(leadInfoMapper).updateById(any(LeadInfoDO.class));
    }
}