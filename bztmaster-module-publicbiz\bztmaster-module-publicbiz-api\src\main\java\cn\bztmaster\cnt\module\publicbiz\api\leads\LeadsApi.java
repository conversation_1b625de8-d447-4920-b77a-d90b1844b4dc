package cn.bztmaster.cnt.module.publicbiz.api.leads;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * 线索中心 API 接口
 *
 * <AUTHOR>
 */
@FeignClient(name = "bztmaster-module-publicbiz")
@Tag(name = "RPC 服务 - 线索中心")
public interface LeadsApi {

    /**
     * 线索分页查询
     *
     * @param pageReqDTO 分页查询参数
     * @return 线索分页结果
     */
    @PostMapping("/api/publicbiz/leads/page")
    @Operation(summary = "线索分页查询")
    CommonResult<PageResult<LeadsRespDTO>> pageLeads(@Valid @RequestBody LeadsPageReqDTO pageReqDTO);

    /**
     * 线索列表查询
     *
     * @param listReqDTO 列表查询参数
     * @return 线索列表
     */
    @PostMapping("/api/publicbiz/leads/list")
    @Operation(summary = "线索列表查询")
    CommonResult<List<LeadsRespDTO>> listLeads(@Valid @RequestBody LeadsListReqDTO listReqDTO);

    /**
     * 获取线索详情
     *
     * @param id 线索ID
     * @return 线索详情
     */
    @PostMapping("/api/publicbiz/leads/get")
    @Operation(summary = "获取线索详情")
    @Parameter(name = "id", description = "线索ID", required = true, example = "1024")
    CommonResult<LeadsRespDTO> getLead(@RequestParam("id") Long id);

    /**
     * 根据线索ID获取线索详情
     *
     * @param leadId 线索ID字符串
     * @return 线索详情
     */
    @PostMapping("/api/publicbiz/leads/get-by-lead-id")
    @Operation(summary = "根据线索ID获取线索详情")
    @Parameter(name = "leadId", description = "线索ID字符串", required = true, example = "LEAD20250721001")
    CommonResult<LeadsRespDTO> getLeadByLeadId(@RequestParam("leadId") String leadId);

    /**
     * 创建线索
     *
     * @param createReqDTO 创建线索请求
     * @return 创建结果
     */
    @PostMapping("/api/publicbiz/leads/create")
    @Operation(summary = "创建线索")
    CommonResult<Long> createLead(@Valid @RequestBody LeadsSaveReqDTO createReqDTO);

    /**
     * 更新线索
     *
     * @param updateReqDTO 更新线索请求
     * @return 更新结果
     */
    @PostMapping("/api/publicbiz/leads/update")
    @Operation(summary = "更新线索")
    CommonResult<Boolean> updateLead(@Valid @RequestBody LeadsSaveReqDTO updateReqDTO);

    /**
     * 删除线索
     *
     * @param id 线索ID
     * @return 删除结果
     */
    @PostMapping("/api/publicbiz/leads/delete")
    @Operation(summary = "删除线索")
    @Parameter(name = "id", description = "线索ID", required = true, example = "1024")
    CommonResult<Boolean> deleteLead(@RequestParam("id") Long id);

    /**
     * 分配线索
     *
     * @param assignReqDTO 分配线索请求
     * @return 分配结果
     */
    @PostMapping("/api/publicbiz/leads/assign")
    @Operation(summary = "分配线索")
    CommonResult<Boolean> assignLead(@Valid @RequestBody LeadsAssignReqDTO assignReqDTO);

    /**
     * 获取线索跟进记录分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 跟进记录分页结果
     */
    @PostMapping("/api/publicbiz/leads/follow-up/page")
    @Operation(summary = "获取线索跟进记录分页")
    CommonResult<PageResult<LeadFollowUpLogRespDTO>> pageLeadFollowUpLogs(@Valid @RequestBody LeadFollowUpLogPageReqDTO pageReqDTO);

    /**
     * 创建线索跟进记录
     *
     * @param createReqDTO 创建跟进记录请求
     * @return 创建结果
     */
    @PostMapping("/api/publicbiz/leads/follow-up/create")
    @Operation(summary = "创建线索跟进记录")
    CommonResult<Long> createLeadFollowUpLog(@Valid @RequestBody LeadFollowUpLogSaveReqDTO createReqDTO);

    /**
     * 更新线索跟进记录
     *
     * @param updateReqDTO 更新跟进记录请求
     * @return 更新结果
     */
    @PostMapping("/api/publicbiz/leads/follow-up/update")
    @Operation(summary = "更新线索跟进记录")
    CommonResult<Boolean> updateLeadFollowUpLog(@Valid @RequestBody LeadFollowUpLogSaveReqDTO updateReqDTO);

    /**
     * 删除线索跟进记录
     *
     * @param id 跟进记录ID
     * @return 删除结果
     */
    @PostMapping("/api/publicbiz/leads/follow-up/delete")
    @Operation(summary = "删除线索跟进记录")
    @Parameter(name = "id", description = "跟进记录ID", required = true, example = "1024")
    CommonResult<Boolean> deleteLeadFollowUpLog(@RequestParam("id") Long id);
}