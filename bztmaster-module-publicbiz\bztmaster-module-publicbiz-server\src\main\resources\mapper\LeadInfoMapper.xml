<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.leads.LeadInfoMapper">

    <select id="selectByLeadId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadInfoDO">
        SELECT * FROM publicbiz_lead_info
        WHERE deleted = 0
        AND lead_id = #{leadId}
        LIMIT 1
    </select>
    
    <select id="selectListByCustomerPhone" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadInfoDO">
        SELECT * FROM publicbiz_lead_info
        WHERE deleted = 0
        AND customer_phone = #{customerPhone}
        ORDER BY id DESC
    </select>
    
    <select id="selectListByCustomerNameLike" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadInfoDO">
        SELECT * FROM publicbiz_lead_info
        WHERE deleted = 0
        AND customer_name LIKE CONCAT('%', #{customerName}, '%')
        ORDER BY id DESC
    </select>
    
    <select id="selectListByLeadSource" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadInfoDO">
        SELECT * FROM publicbiz_lead_info
        WHERE deleted = 0
        AND lead_source = #{leadSource}
        ORDER BY id DESC
    </select>
    
    <select id="selectListByBusinessModule" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadInfoDO">
        SELECT * FROM publicbiz_lead_info
        WHERE deleted = 0
        AND business_module = #{businessModule}
        ORDER BY id DESC
    </select>
    
    <select id="selectListByLeadStatus" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadInfoDO">
        SELECT * FROM publicbiz_lead_info
        WHERE deleted = 0
        AND lead_status = #{leadStatus}
        ORDER BY id DESC
    </select>
    
    <select id="selectListByCurrentOwner" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadInfoDO">
        SELECT * FROM publicbiz_lead_info
        WHERE deleted = 0
        AND current_owner = #{currentOwner}
        ORDER BY id DESC
    </select>
</mapper>