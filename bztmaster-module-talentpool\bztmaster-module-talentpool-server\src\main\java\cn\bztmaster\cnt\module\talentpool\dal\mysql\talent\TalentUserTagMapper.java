package cn.bztmaster.cnt.module.talentpool.dal.mysql.talent;

import cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentUserTagDO;
import org.apache.ibatis.annotations.Mapper;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Param;
import cn.bztmaster.cnt.framework.tenant.core.context.TenantContextHolder;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.util.List;

@Mapper
public interface TalentUserTagMapper extends BaseMapperX<TalentUserTagDO> {
    // 可扩展自定义方法
    default List<TalentUserTagDO> selectListByUserIds(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return java.util.Collections.emptyList();
        }
        Long tenantId = TenantContextHolder.getTenantId();
        return selectList(new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<TalentUserTagDO>()
                .in(TalentUserTagDO::getUserId, userIds)
                .eq(TalentUserTagDO::getDeleted, false)
                .eq(TalentUserTagDO::getTenantId, tenantId));
    }

    @Select("SELECT * FROM talent_user_tag WHERE user_id = #{userId} AND deleted = 0")
    java.util.List<cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentUserTagDO> selectListByUserId(
            @Param("userId") Long userId);

    @Delete("DELETE FROM talent_user_tag WHERE user_id = #{userId}")
    void deleteByUserId(@Param("userId") Long userId);
}