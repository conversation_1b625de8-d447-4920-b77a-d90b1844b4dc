package cn.bztmaster.cnt.module.system.api.material;

import cn.hutool.core.convert.Convert;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.util.collection.CollectionUtils;
import cn.bztmaster.cnt.module.system.api.material.dto.CategoryListRespDTO;
import cn.bztmaster.cnt.module.system.enums.ApiConstants;
import com.fhs.core.trans.anno.AutoTrans;
import com.fhs.trans.service.AutoTransable;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.*;

import static cn.bztmaster.cnt.module.system.api.material.MaterialCategoryApi.PREFIX;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 素材分类")
@AutoTrans(namespace = PREFIX, fields = {"name"})
public interface MaterialCategoryApi extends AutoTransable<CategoryListRespDTO> {

    String PREFIX = ApiConstants.PREFIX + "/material/category";

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "通过分类 ID 查询分类")
    @Parameter(name = "id", description = "分类编号", example = "1", required = true)
    CommonResult<CategoryListRespDTO> getCategory(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "通过分类 ID 查询分类们")
    @Parameter(name = "ids", description = "分类编号数组", example = "1,2", required = true)
    CommonResult<List<CategoryListRespDTO>> getCategoryList(@RequestParam("ids") Collection<Long> ids);

    @GetMapping(PREFIX + "/list-by-parent")
    @Operation(summary = "通过父分类 ID 查询子分类")
    @Parameter(name = "parentId", description = "父分类编号", example = "1", required = true)
    CommonResult<List<CategoryListRespDTO>> getCategoryListByParentId(@RequestParam("parentId") Long parentId);

    @GetMapping(PREFIX + "/list-all")
    @Operation(summary = "获取所有分类列表")
    CommonResult<List<CategoryListRespDTO>> getCategoryListAll();

    /**
     * 获得分类 Map
     *
     * @param ids 分类编号数组
     * @return 分类 Map
     */
    default Map<Long, CategoryListRespDTO> getCategoryMap(Collection<Long> ids) {
        List<CategoryListRespDTO> categories = getCategoryList(ids).getCheckedData();
        return CollectionUtils.convertMap(categories, CategoryListRespDTO::getId);
    }

    /**
     * 校验分类是否有效。如下情况，视为无效：
     * 1. 分类编号不存在
     * 2. 分类被禁用
     *
     * @param id 分类编号
     */
    default void validateCategory(Long id) {
        validateCategoryList(Collections.singleton(id));
    }

    @GetMapping(PREFIX + "/valid")
    @Operation(summary = "校验分类们是否有效")
    @Parameter(name = "ids", description = "分类编号数组", example = "3,5", required = true)
    CommonResult<Boolean> validateCategoryList(@RequestParam("ids") Collection<Long> ids);

    @Override
    @GetMapping("select")
    default List<CategoryListRespDTO> selectByIds(List<?> ids) {
        return getCategoryList(Convert.toList(Long.class, ids)).getCheckedData();
    }

    @Override
    @GetMapping("select-list")
    default CategoryListRespDTO selectById(Object id) {
        return getCategory(Convert.toLong(id)).getCheckedData();
    }
} 