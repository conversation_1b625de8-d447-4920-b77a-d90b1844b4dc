package cn.bztmaster.cnt.module.publicbiz.service.digitalAsset.impl;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.digitalAsset.CourseLessonConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset.CourseLessonDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.digitalAsset.CourseLessonMapper;
import cn.bztmaster.cnt.module.publicbiz.service.digitalAsset.CourseLessonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.*;

/**
 * 课程课时 Service 实现类
 * 
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CourseLessonServiceImpl implements CourseLessonService {

    @Resource
    private CourseLessonMapper courseLessonMapper;

    @Override
    public Long createCourseLesson(CourseLessonSaveReqVO reqVO) {
        // 转换并插入
        CourseLessonDO lesson = CourseLessonConvert.INSTANCE.convert(reqVO);
        courseLessonMapper.insert(lesson);
        return lesson.getId();
    }

    @Override
    public void updateCourseLesson(CourseLessonSaveReqVO reqVO) {
        // 校验存在
        validateLessonExists(reqVO.getId());
        // 转换并更新
        CourseLessonDO updateObj = CourseLessonConvert.INSTANCE.convert(reqVO);
        courseLessonMapper.updateById(updateObj);
    }

    @Override
    public void deleteCourseLesson(Long id) {
        // 校验存在
        validateLessonExists(id);
        // 删除
        courseLessonMapper.deleteById(id);
    }

    private void validateLessonExists(Long id) {
        if (courseLessonMapper.selectById(id) == null) {
            throw exception(COURSE_LESSON_NOT_EXISTS);
        }
    }
}
