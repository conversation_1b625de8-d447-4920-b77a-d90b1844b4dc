package cn.bztmaster.cnt.module.system.service.material;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;

public interface MaterialNewsService {
    PageResult<NewsRespVO> getNewsPage(NewsPageReqVO reqVO);
    Long createNews(NewsSaveReqVO reqVO);
    void updateNews(NewsSaveReqVO reqVO);
    void deleteNews(Long id);
    NewsRespVO getNewsDetail(Long id);
    PageResult<NewsRespVO> getNewsRecyclePage(NewsPageReqVO reqVO, String updateTimeFrom, String updateTimeTo);
    void restoreNewsFromRecycle(java.util.List<Long> idList);
    void deleteNewsFromRecycle(java.util.List<Long> idList);
} 