# 线索中心实施计划

- [x] 1. 创建数据库表结构

  - 创建 lead_info 表，包含线索基本信息字段
  - 创建 lead_follow_up_log 表，包含跟进记录字段
  - 添加必要的索引和外键约束
  - _需求: 1, 2, 3, 4_

- [x] 2. 实现线索相关枚举类

  - 创建 LeadSourceEnum（线索来源枚举）
  - 创建 BusinessModuleEnum（业务模块枚举）
  - 创建 LeadStatusEnum（线索状态枚举）
  - 创建 CreateMethodEnum（创建方式枚举）
  - _需求: 1, 2, 3, 4_

- [x] 3. 实现数据访问层

  - [x] 3.1 创建数据对象(DO)类

    - 实现 LeadInfoDO 类，映射 lead_info 表
    - 实现 LeadFollowUpLogDO 类，映射 lead_follow_up_log 表
    - 添加 MyBatis-Plus 注解和字段校验
    - _需求: 1, 4_

  - [x] 3.2 创建 Mapper 接口

    - 实现 LeadInfoMapper 接口，继承 BaseMapper 提供 CRUD 功能
    - 实现 LeadFollowUpLogMapper 接口
    - 添加自定义查询方法（如分页查询、条件查询）
    - _需求: 1, 2, 4_

- [x] 4. 实现 API 接口层

  - [x] 4.1 创建 DTO 数据传输对象

    - 实现 LeadsRespDTO、LeadsSaveReqDTO、LeadsPageReqDTO 等
    - 实现 LeadFollowUpLogRespDTO、LeadFollowUpLogSaveReqDTO 等
    - 添加参数校验注解
    - _需求: 1, 2, 4_

  - [x] 4.2 创建 Feign API 接口

    - 实现 LeadsApi 接口，定义远程调用方法
    - 添加 Swagger 注解和参数说明
    - _需求: 1, 2, 3, 4_

- [x] 5. 实现业务服务层

  - [x] 5.1 创建 Service 接口和实现类

    - 实现 LeadsService 接口，定义核心业务方法
    - 实现 LeadsServiceImpl 类，包含线索 CRUD、分页查询、分配等业务逻辑
    - 添加事务注解和异常处理
    - _需求: 1, 2, 3, 4_

  - [x] 5.2 创建跟进记录 Service

    - 实现 LeadFollowUpLogService 接口和实现类
    - 实现跟进记录的增删改查功能
    - _需求: 4_

- [x] 6. 实现控制器层

  - [x] 6.1 创建 VO 对象

    - 实现 LeadsRespVO、LeadsSaveReqVO、LeadsPageReqVO 等前端交互对象
    - 实现 LeadFollowUpLogRespVO、LeadFollowUpLogSaveReqVO 等
    - 添加参数校验和 Swagger 注解
    - _需求: 1, 2, 4_

  - [x] 6.2 创建 Controller 类

    - 实现 LeadsController，包含分页查询、新增、编辑、删除、分配等接口
    - 实现 LeadFollowUpController，处理跟进记录相关接口
    - 添加统一的响应格式和异常处理
    - _需求: 1, 2, 3, 4_

- [x] 7. 实现对象转换层

  - 创建 LeadsConvert 类，实现 VO、DTO、DO 之间的转换
  - 创建 LeadFollowUpLogConvert 类
  - 使用 MapStruct 注解实现自动转换
  - _需求: 1, 2, 4_

- [x] 8. 编写单元测试

  - [x] 8.1 Service 层测试

    - 编写 LeadsServiceTest，测试核心业务逻辑
    - 编写 LeadFollowUpLogServiceTest
    - 使用 Mock 对象模拟依赖
    - _需求: 1, 2, 3, 4_

  - [x] 8.2 Controller 层测试

    - 编写 LeadsControllerTest，测试接口功能
    - 编写 LeadFollowUpControllerTest
    - 测试参数校验和异常处理
    - _需求: 1, 2, 3, 4_

- [x] 9. 配置和集成

  - 配置 Swagger 文档
  - 集成到主应用模块
  - 验证完整功能流程
  - _需求: 1, 2, 3, 4_
