package cn.bztmaster.cnt.module.infra.dal.mysql.demo;

import java.util.*;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.module.infra.dal.dataobject.demo.InfraStudentContactDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 学生联系人 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfraStudentContactMapper extends BaseMapperX<InfraStudentContactDO> {

    default List<InfraStudentContactDO> selectListByStudentId(Long studentId) {
        return selectList(InfraStudentContactDO::getStudentId, studentId);
    }

    default int deleteByStudentId(Long studentId) {
        return delete(InfraStudentContactDO::getStudentId, studentId);
    }

}