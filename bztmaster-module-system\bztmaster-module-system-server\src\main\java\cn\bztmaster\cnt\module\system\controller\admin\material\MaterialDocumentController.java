package cn.bztmaster.cnt.module.system.controller.admin.material;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.service.material.MaterialDocumentService;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/system/material/document")
@Tag(name = "素材库-文档管理")
@Slf4j
@Validated
public class MaterialDocumentController {
    @Resource
    private MaterialDocumentService documentService;

    @GetMapping("/list")
    @Operation(summary = "文档列表")
    public CommonResult<PageResult<DocumentRespVO>> list(@Validated DocumentPageReqVO reqVO) {
        return CommonResult.success(documentService.getDocumentPage(reqVO));
    }

    @PostMapping("/create")
    @Operation(summary = "新增文档")
    public CommonResult<Long> create(@RequestBody @Validated DocumentSaveReqVO reqVO) {
        return CommonResult.success(documentService.createDocument(reqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "编辑文档")
    public CommonResult<Boolean> update(@RequestBody @Validated DocumentSaveReqVO reqVO) {
        documentService.updateDocument(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除文档")
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        documentService.deleteDocument(id);
        return CommonResult.success(true);
    }

    @GetMapping("/detail")
    @Operation(summary = "文档详情")
    public CommonResult<DocumentRespVO> detail(@RequestParam("id") Long id) {
        return CommonResult.success(documentService.getDocumentDetail(id));
    }

    @GetMapping("/recycleList")
    @Operation(summary = "文档回收站列表")
    public CommonResult<PageResult<DocumentRespVO>> recycleList(@Validated DocumentPageReqVO reqVO,
                                                               @RequestParam(value = "updateTimeFrom", required = false) String updateTimeFrom,
                                                               @RequestParam(value = "updateTimeTo", required = false) String updateTimeTo) {
        return CommonResult.success(documentService.getDocumentRecyclePage(reqVO, updateTimeFrom, updateTimeTo));
    }

    @PostMapping("/recycleRestore")
    @Operation(summary = "文档回收站恢复")
    public CommonResult<Boolean> recycleRestore(@RequestBody List<Long> idList) {
        documentService.restoreDocumentFromRecycle(idList);
        return CommonResult.success(true);
    }

    @PostMapping("/recycleDelete")
    @Operation(summary = "文档回收站永久删除")
    public CommonResult<Boolean> recycleDelete(@RequestBody List<Long> idList) {
        documentService.deleteDocumentFromRecycle(idList);
        return CommonResult.success(true);
    }
} 