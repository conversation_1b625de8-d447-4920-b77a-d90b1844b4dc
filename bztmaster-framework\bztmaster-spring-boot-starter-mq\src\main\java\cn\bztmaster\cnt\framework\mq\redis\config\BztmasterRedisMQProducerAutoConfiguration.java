package cn.bztmaster.cnt.framework.mq.redis.config;

import cn.bztmaster.cnt.framework.mq.redis.core.RedisMQTemplate;
import cn.bztmaster.cnt.framework.mq.redis.core.interceptor.RedisMessageInterceptor;
import cn.bztmaster.cnt.framework.redis.config.BztmasterRedisAutoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.List;

/**
 * Redis 消息队列 Producer 配置类
 *
 * <AUTHOR>
 */
@Slf4j
@AutoConfiguration(after = BztmasterRedisAutoConfiguration.class)
public class BztmasterRedisMQProducerAutoConfiguration {

    @Bean
    public RedisMQTemplate redisMQTemplate(StringRedisTemplate redisTemplate,
                                           List<RedisMessageInterceptor> interceptors) {
        RedisMQTemplate redisMQTemplate = new RedisMQTemplate(redisTemplate);
        // 添加拦截器
        interceptors.forEach(redisMQTemplate::addInterceptor);
        return redisMQTemplate;
    }

}
