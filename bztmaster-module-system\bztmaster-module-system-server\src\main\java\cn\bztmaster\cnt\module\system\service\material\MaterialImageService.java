package cn.bztmaster.cnt.module.system.service.material;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;

public interface MaterialImageService {
    PageResult<ImageRespVO> getImagePage(ImagePageReqVO reqVO);
    Long createImage(ImageSaveReqVO reqVO);
    void updateImage(ImageSaveReqVO reqVO);
    void deleteImage(Long id);
    ImageRespVO getImageDetail(Long id);
    PageResult<ImageRespVO> getImageRecyclePage(ImagePageReqVO reqVO, String updateTimeFrom, String updateTimeTo);
    void restoreImageFromRecycle(java.util.List<Long> idList);
    void deleteImageFromRecycle(java.util.List<Long> idList);
} 