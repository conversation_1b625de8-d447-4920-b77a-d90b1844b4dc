package cn.bztmaster.cnt.module.trade.controller.admin.aftersale.vo;

import cn.bztmaster.cnt.module.trade.controller.admin.base.member.user.MemberUserRespVO;
import cn.bztmaster.cnt.module.trade.controller.admin.base.product.property.ProductPropertyValueDetailRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 交易售后分页的每一条记录 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AfterSaleRespPageItemVO extends AfterSaleBaseVO {

    @Schema(description = "售后编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "27630")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    /**
     * 商品属性数组
     */
    private List<ProductPropertyValueDetailRespVO> properties;

    /**
     * 用户信息
     */
    private MemberUserRespVO user;

}
