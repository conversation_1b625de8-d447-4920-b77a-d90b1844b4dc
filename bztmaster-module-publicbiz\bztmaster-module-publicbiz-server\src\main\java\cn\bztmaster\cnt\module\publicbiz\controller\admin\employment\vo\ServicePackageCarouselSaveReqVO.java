package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;

@Data
@Schema(description = "就业服务-服务套餐轮播图保存 Request VO")
public class ServicePackageCarouselSaveReqVO {
    @Schema(description = "轮播图ID（更新时使用）")
    private Long id;

    @Schema(description = "轮播图URL", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "轮播图URL不能为空")
    private String imageUrl;

    @Schema(description = "排序")
    private Integer sortOrder;

    @Schema(description = "状态")
    private Integer status;
} 