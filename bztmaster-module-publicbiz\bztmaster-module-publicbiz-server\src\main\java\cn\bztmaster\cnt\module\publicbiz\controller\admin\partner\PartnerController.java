package cn.bztmaster.cnt.module.publicbiz.controller.admin.partner;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.partner.PartnerService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo.PartnerPageReqVO;

@RestController
@RequestMapping("/publicbiz/partner")
@Tag(name = "资源中心-合作伙伴管理")
public class PartnerController {

    @Resource
    private PartnerService partnerService;

    @GetMapping("/page")
    @Operation(summary = "合作伙伴分页列表")
    public CommonResult<PageResult<PartnerRespVO>> page(PartnerPageReqVO reqVO) {
        return CommonResult.success(partnerService.getPartnerPage(reqVO));
    }

    @PostMapping("/create")
    @Operation(summary = "新增合作伙伴")
    public CommonResult<Boolean> create(@RequestBody PartnerSaveReqVO reqVO) {
        partnerService.createPartner(reqVO);

        return CommonResult.success(true);
    }

    @PostMapping("/update")
    @Operation(summary = "编辑合作伙伴")
    public CommonResult<Boolean> update(@RequestBody PartnerUpdateReqVO reqVO) {
        partnerService.updatePartner(reqVO);
        return CommonResult.success(true);
    }

    @GetMapping("/detail")
    @Operation(summary = "合作伙伴详情")
    public CommonResult<PartnerRespVO> detail(@RequestParam("id") Long id) {
        return CommonResult.success(partnerService.getPartnerDetail(id));
    }

    @PostMapping("/delete")
    @Operation(summary = "逻辑删除合作伙伴")
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        partnerService.deletePartner(id);
        return CommonResult.success(true);
    }

    @GetMapping("/stat")
    @Operation(summary = "合作伙伴统计卡片数据")
    public CommonResult<PartnerStatRespVO> stat() {
        return CommonResult.success(partnerService.getPartnerStat());
    }

    @GetMapping("/list/active")
    @Operation(summary = "获取有效状态合作伙伴列表（下拉框数据）")
    public CommonResult<PartnerActiveListRespVO> listActive(
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "biz", required = false) String biz,
            @RequestParam(value = "keyword", required = false) String keyword) {
        PartnerActiveListRespVO respVO = partnerService.getActivePartnerList(status, type, biz, keyword);
        return CommonResult.success(respVO);
    }
}
