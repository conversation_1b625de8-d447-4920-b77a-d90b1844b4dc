package cn.bztmaster.cnt.module.system.dal.mysql.material;

import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialArticleDO;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.system.controller.admin.material.vo.ArticlePageReqVO;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Mapper
public interface MaterialArticleMapper extends BaseMapperX<MaterialArticleDO> {

    default PageResult<MaterialArticleDO> selectPage(ArticlePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MaterialArticleDO>()
                .likeIfPresent(MaterialArticleDO::getTitle, reqVO.getTitle())
                .eqIfPresent(MaterialArticleDO::getSourceOrgId, reqVO.getSourceOrgId())
                .eqIfPresent(MaterialArticleDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(MaterialArticleDO::getVisibleOrgId, reqVO.getVisibleOrgId())
                .eq(MaterialArticleDO::getDeleted, false)
                .orderByDesc(MaterialArticleDO::getId));
    }

    default PageResult<MaterialArticleDO> selectRecyclePage(ArticlePageReqVO reqVO, String updateTimeFrom, String updateTimeTo) {
        // 自动限定30天内
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime now = LocalDateTime.now();
        if (updateTimeTo == null) updateTimeTo = now.format(formatter);
        if (updateTimeFrom == null) updateTimeFrom = now.minusDays(30).format(formatter);
        return selectPage(reqVO, new LambdaQueryWrapperX<MaterialArticleDO>()
                .likeIfPresent(MaterialArticleDO::getTitle, reqVO.getTitle())
                .eqIfPresent(MaterialArticleDO::getSourceOrgId, reqVO.getSourceOrgId())
                .eqIfPresent(MaterialArticleDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(MaterialArticleDO::getVisibleOrgId, reqVO.getVisibleOrgId())
                .eq(MaterialArticleDO::getDeleted, true)
                .between(MaterialArticleDO::getUpdateTime, updateTimeFrom, updateTimeTo)
                .orderByDesc(MaterialArticleDO::getUpdateTime));
    }

    int restoreFromRecycle(java.util.List<Long> idList);

    int deleteFromRecycle(java.util.List<Long> idList);

    @org.apache.ibatis.annotations.Update("UPDATE mp_material_article SET deleted = 1 WHERE id = #{id}")
    int logicDeleteById(Long id);
}