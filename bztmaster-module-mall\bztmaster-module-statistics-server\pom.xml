<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.bztmaster.cnt</groupId>
        <artifactId>bztmaster-module-mall</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>bztmaster-module-statistics-server</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        statistics 模块，主要实现统计相关功能
        例如：统计商品、会员、交易等功能。
    </description>

    <dependencies>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-env</artifactId>
        </dependency>

        <!-- 依赖服务 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-module-statistics-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-module-promotion-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-module-product-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-module-trade-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-module-member-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-module-pay-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-biz-ip</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-rpc</artifactId>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-test</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-excel</artifactId>
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-monitor</artifactId>
        </dependency>
    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
