package cn.bztmaster.cnt.module.pay.api.refund;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.module.pay.api.refund.dto.PayRefundCreateReqDTO;
import cn.bztmaster.cnt.module.pay.api.refund.dto.PayRefundRespDTO;
import cn.bztmaster.cnt.module.pay.convert.refund.PayRefundConvert;
import cn.bztmaster.cnt.module.pay.dal.dataobject.refund.PayRefundDO;
import cn.bztmaster.cnt.module.pay.service.refund.PayRefundService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class PayRefundApiImpl implements PayRefundApi {

    @Resource
    private PayRefundService payRefundService;

    @Override
    public CommonResult<Long> createRefund(PayRefundCreateReqDTO reqDTO) {
        return success(payRefundService.createRefund(reqDTO));
    }

    @Override
    public CommonResult<PayRefundRespDTO> getRefund(Long id) {
        PayRefundDO refund = payRefundService.getRefund(id);
        return success(BeanUtils.toBean(refund, PayRefundRespDTO.class));
    }

}
