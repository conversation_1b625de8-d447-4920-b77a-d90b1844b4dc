package cn.bztmaster.cnt.module.publicbiz.api.leads.dto;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 线索跟进记录分页查询 Request DTO
 *
 * <AUTHOR>
 */
@Schema(description = "线索跟进记录分页查询请求 DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LeadFollowUpLogPageReqDTO extends PageParam {

    @Schema(description = "关联的线索ID", example = "LEAD20250721001")
    private String leadId;

    @Schema(description = "创建人", example = "admin")
    private String creator;

    @Schema(description = "创建时间范围开始", example = "2025-07-01 00:00:00")
    private String beginCreateTime;

    @Schema(description = "创建时间范围结束", example = "2025-07-21 23:59:59")
    private String endCreateTime;
}