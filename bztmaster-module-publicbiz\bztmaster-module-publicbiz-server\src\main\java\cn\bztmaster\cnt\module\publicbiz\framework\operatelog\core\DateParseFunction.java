package cn.bztmaster.cnt.module.publicbiz.framework.operatelog.core;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.mzt.logapi.service.IParseFunction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 日期格式化函数，用于操作日志中的日期字段显示
 * 将Date对象格式化为 yyyy-MM-dd 格式
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class DateParseFunction implements IParseFunction {

    public static final String NAME = "formatDate";

    @Override
    public boolean executeBefore() {
        return false; // 后转换值，确保在比较时使用格式化后的值
    }

    @Override
    public String functionName() {
        return NAME;
    }

    @Override
    public String apply(Object value) {
        if (value == null) {
            return "";
        }
        
        if (value instanceof Date) {
            return DateUtil.format((Date) value, "yyyy-MM-dd");
        }
        
        if (value instanceof String) {
            String dateStr = (String) value;
            if (StrUtil.isNotEmpty(dateStr)) {
                try {
                    Date date = DateUtil.parse(dateStr);
                    return DateUtil.format(date, "yyyy-MM-dd");
                } catch (Exception e) {
                    log.warn("[apply][日期格式化失败，原值：{}]", dateStr, e);
                    return dateStr;
                }
            }
        }
        
        return value.toString();
    }
} 