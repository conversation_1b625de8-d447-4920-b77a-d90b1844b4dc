package cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.digitalAsset.DigitalAssetCourseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 数字资产课程管理 Controller
 * 对应前端页面：src/views/infra/ResourceCenter/DigitalAsset/DigitalAsset.vue
 * 
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 数字资产课程管理")
@RestController
@RequestMapping("/publicbiz/digital-asset/course")
@Validated
public class DigitalAssetCourseController {

    @Resource
    private DigitalAssetCourseService digitalAssetCourseService;

    @GetMapping("/page")
    @Operation(summary = "分页查询课程列表")
    public CommonResult<PageResult<DigitalAssetCourseRespVO>> pageDigitalAssetCourse(@Valid DigitalAssetCoursePageReqVO reqVO) {
        PageResult<DigitalAssetCourseRespVO> pageResult = digitalAssetCourseService.pageDigitalAssetCourse(reqVO);
        return CommonResult.success(pageResult);
    }

    @PostMapping("/create")
    @Operation(summary = "新增课程")
    public CommonResult<Long> createDigitalAssetCourse(@Valid @RequestBody DigitalAssetCourseSaveReqVO reqVO) {
        Long courseId = digitalAssetCourseService.createDigitalAssetCourse(reqVO);
        return CommonResult.success(courseId);
    }

    @PutMapping("/update")
    @Operation(summary = "更新课程")
    public CommonResult<Boolean> updateDigitalAssetCourse(@Valid @RequestBody DigitalAssetCourseSaveReqVO reqVO) {
        digitalAssetCourseService.updateDigitalAssetCourse(reqVO);
        return CommonResult.success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除课程")
    @Parameter(name = "id", description = "课程ID", required = true)
    public CommonResult<Boolean> deleteDigitalAssetCourse(@RequestParam("id") Long id) {
        digitalAssetCourseService.deleteDigitalAssetCourse(id);
        return CommonResult.success(true);
    }

    @GetMapping("/get/{id}")
    @Operation(summary = "获取课程详情")
    @Parameter(name = "id", description = "课程ID", required = true)
    public CommonResult<DigitalAssetCourseRespVO> getDigitalAssetCourse(@PathVariable("id") Long id) {
        DigitalAssetCourseRespVO course = digitalAssetCourseService.getDigitalAssetCourse(id);
        return CommonResult.success(course);
    }

    @PutMapping("/status/{id}")
    @Operation(summary = "修改课程状态")
    @Parameter(name = "id", description = "课程ID", required = true)
    public CommonResult<Boolean> updateDigitalAssetCourseStatus(@PathVariable("id") Long id, 
                                                               @Valid @RequestBody DigitalAssetCourseStatusReqVO reqVO) {
        digitalAssetCourseService.updateDigitalAssetCourseStatus(id, reqVO.getStatus());
        return CommonResult.success(true);
    }

    @GetMapping("/statistics/overview")
    @Operation(summary = "获取课程统计概览")
    public CommonResult<CourseStatisticsRespVO> getCourseStatistics() {
        CourseStatisticsRespVO statistics = digitalAssetCourseService.getCourseStatistics();
        return CommonResult.success(statistics);
    }
}
