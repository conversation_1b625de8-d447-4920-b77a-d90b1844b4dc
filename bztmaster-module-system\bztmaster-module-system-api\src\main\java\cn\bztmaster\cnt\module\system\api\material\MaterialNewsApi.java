package cn.bztmaster.cnt.module.system.api.material;

import cn.hutool.core.convert.Convert;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.util.collection.CollectionUtils;
import cn.bztmaster.cnt.module.system.api.material.dto.NewsRespDTO;
import cn.bztmaster.cnt.module.system.enums.ApiConstants;
import com.fhs.core.trans.anno.AutoTrans;
import com.fhs.trans.service.AutoTransable;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.*;

import static cn.bztmaster.cnt.module.system.api.material.MaterialNewsApi.PREFIX;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 图文素材")
@AutoTrans(namespace = PREFIX, fields = {"name"})
public interface MaterialNewsApi extends AutoTransable<NewsRespDTO> {

    String PREFIX = ApiConstants.PREFIX + "/material/news";

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "通过图文 ID 查询图文")
    @Parameter(name = "id", description = "图文编号", example = "1", required = true)
    CommonResult<NewsRespDTO> getNews(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "通过图文 ID 查询图文们")
    @Parameter(name = "ids", description = "图文编号数组", example = "1,2", required = true)
    CommonResult<List<NewsRespDTO>> getNewsList(@RequestParam("ids") Collection<Long> ids);

    @GetMapping(PREFIX + "/list-by-category")
    @Operation(summary = "通过分类 ID 查询图文们")
    @Parameter(name = "categoryId", description = "分类编号", example = "1", required = true)
    CommonResult<List<NewsRespDTO>> getNewsListByCategoryId(@RequestParam("categoryId") Long categoryId);

    @GetMapping(PREFIX + "/list-by-org")
    @Operation(summary = "通过来源机构 ID 查询图文们")
    @Parameter(name = "sourceOrgId", description = "来源机构编号", example = "1", required = true)
    CommonResult<List<NewsRespDTO>> getNewsListBySourceOrgId(@RequestParam("sourceOrgId") Long sourceOrgId);

    @GetMapping(PREFIX + "/recycleList")
    @Operation(summary = "图文回收站列表")
    CommonResult<List<NewsRespDTO>> getNewsRecycleList(@RequestParam Map<String, Object> params);

    @PostMapping(PREFIX + "/recycleRestore")
    @Operation(summary = "图文回收站恢复")
    CommonResult<Boolean> recycleRestore(@RequestBody List<Long> idList);

    @PostMapping(PREFIX + "/recycleDelete")
    @Operation(summary = "图文回收站永久删除")
    CommonResult<Boolean> recycleDelete(@RequestBody List<Long> idList);

    /**
     * 获得图文 Map
     *
     * @param ids 图文编号数组
     * @return 图文 Map
     */
    default Map<Long, NewsRespDTO> getNewsMap(Collection<Long> ids) {
        List<NewsRespDTO> news = getNewsList(ids).getCheckedData();
        return CollectionUtils.convertMap(news, NewsRespDTO::getId);
    }

    /**
     * 校验图文是否有效。如下情况，视为无效：
     * 1. 图文编号不存在
     * 2. 图文被禁用
     *
     * @param id 图文编号
     */
    default void validateNews(Long id) {
        validateNewsList(Collections.singleton(id));
    }

    @GetMapping(PREFIX + "/valid")
    @Operation(summary = "校验图文们是否有效")
    @Parameter(name = "ids", description = "图文编号数组", example = "3,5", required = true)
    CommonResult<Boolean> validateNewsList(@RequestParam("ids") Collection<Long> ids);

    @Override
    @GetMapping("select")
    default List<NewsRespDTO> selectByIds(List<?> ids) {
        return getNewsList(Convert.toList(Long.class, ids)).getCheckedData();
    }

    @Override
    @GetMapping("select-list")
    default NewsRespDTO selectById(Object id) {
        return getNews(Convert.toLong(id)).getCheckedData();
    }
} 