package cn.bztmaster.cnt.module.publicbiz.enums.leads;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务模块枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BusinessModuleEnum {

    UNIVERSITY_BUSINESS(1, "高校业务"),
    DOMESTIC_SERVICE(2, "家政业务"),
    TRAINING_BUSINESS(3, "培训业务"),
    CERTIFICATION_BUSINESS(4, "认证业务");

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据类型获取枚举
     *
     * @param type 类型
     * @return 枚举
     */
    public static BusinessModuleEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (BusinessModuleEnum value : BusinessModuleEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

}