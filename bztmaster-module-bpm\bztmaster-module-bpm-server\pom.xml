<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.bztmaster.cnt</groupId>
        <artifactId>bztmaster-module-bpm</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>bztmaster-module-bpm-server</artifactId>

    <name>${project.artifactId}</name>
    <description>
        bpm 包下，业务流程管理（Business Process Management），我们放工作流的功能，基于 Flowable 6 版本实现。
        例如说：流程定义、表单配置、审核中心（我的申请、我的待办、我的已办）等等    </description>

    <dependencies>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-env</artifactId>
        </dependency>

        <!-- 依赖服务 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-module-bpm-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-biz-tenant</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-rpc</artifactId>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- 服务保障相关 TODO 芋艿：暂时去掉 -->
        <!--        <dependency>-->
        <!--            <groupId>cn.bztmaster.cnt</groupId>-->
        <!--            <artifactId>bztmaster-spring-boot-starter-protection</artifactId>-->
        <!--        </dependency>-->

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-test</artifactId>
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-monitor</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>cn.bztmaster.cnt</groupId>
            <artifactId>bztmaster-spring-boot-starter-excel</artifactId>
        </dependency>

        <!-- Flowable 工作流相关 -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter-process</artifactId>
        </dependency>
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter-actuator</artifactId>
        </dependency>
    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
