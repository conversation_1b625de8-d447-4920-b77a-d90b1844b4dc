package cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo.*;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo.TeacherImportReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo.TeacherImportRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo.TeacherImportValidateRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo.TeacherImportExcelVO;
import cn.bztmaster.cnt.module.publicbiz.service.teacher.TeacherService;
import cn.bztmaster.cnt.framework.excel.core.util.ExcelUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import java.util.List;
import lombok.Data;

@RestController
@RequestMapping("/publicbiz/teacher")
@Tag(name = "师资库-讲师管理")
public class TeacherController {
    @Resource
    private TeacherService teacherService;

    @PostMapping("/create")
    @Operation(summary = "新增讲师")
    public CommonResult<Long> create(@RequestBody TeacherSaveReqVO reqVO) {
        return CommonResult.success(teacherService.createTeacher(reqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "编辑讲师")
    public CommonResult<Boolean> update(@RequestBody TeacherUpdateReqVO reqVO) {
        teacherService.updateTeacher(reqVO);
        return CommonResult.success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除讲师", description = "根据讲师ID删除讲师（逻辑删除）")
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        // 调用服务层逻辑删除讲师
        teacherService.deleteTeacher(id);
        return CommonResult.success(true);
    }

    @GetMapping("/detail")
    @Operation(summary = "讲师详情")
    public CommonResult<TeacherRespVO> detail(@RequestParam("id") Long id) {
        return CommonResult.success(teacherService.getTeacherDetail(id));
    }

    @GetMapping("/page")
    @Operation(summary = "讲师分页列表")
    public CommonResult<PageResult<TeacherRespVO>> page(TeacherPageReqVO reqVO) {
        return CommonResult.success(teacherService.getTeacherPage(reqVO));
    }

    @GetMapping("/list")
    @Operation(summary = "讲师列表（不分页）")
    public CommonResult<List<TeacherListRespVO>> list() {
        return CommonResult.success(teacherService.getTeacherList());
    }

    @GetMapping("/stat")
    @Operation(summary = "讲师统计卡片数据")
    public CommonResult<TeacherStatRespVO> stat() {
        return CommonResult.success(teacherService.getTeacherStat());
    }

    @PostMapping("/import/validate-excel")
    @Operation(summary = "校验Excel导入的讲师数据")
    public CommonResult<TeacherImportValidateRespVO> validateImportExcelTeachers(@RequestParam("file") MultipartFile file) throws Exception {
        List<TeacherImportExcelVO> list = ExcelUtils.read(file, TeacherImportExcelVO.class);
        return CommonResult.success(teacherService.validateImportExcelTeachers(list));
    }

    @PostMapping("/import/execute")
    @Operation(summary = "执行批量导入讲师（只导入校验通过的数据）")
    public CommonResult<TeacherImportRespVO> executeImportTeachers(@RequestBody TeacherImportReqVO reqVO) {
        return CommonResult.success(teacherService.importValidTeachers(reqVO.getTeacherList()));
    }

    @PostMapping("/import")
    @Operation(summary = "批量导入讲师（旧接口，保留兼容性）")
    public CommonResult<Boolean> importTeachers(@RequestParam("file") MultipartFile file) {
        // TODO: 实现文件上传和解析逻辑
        return CommonResult.success(true);
    }

    // VO for id请求
    @Data
    public static class IdReqVO {
        private Long id;
    }
} 