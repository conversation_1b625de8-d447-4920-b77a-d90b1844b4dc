package cn.bztmaster.cnt.module.publicbiz.framework.operatelog.core;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.mzt.logapi.service.IParseFunction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 日期比较函数，用于操作日志中的日期字段比较
 * 只比较日期部分（yyyy-MM-dd），忽略时间部分
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class DateCompareFunction implements IParseFunction {

    public static final String NAME = "compareDate";

    @Override
    public boolean executeBefore() {
        return true; // 先转换值后对比
    }

    @Override
    public String functionName() {
        return NAME;
    }

    @Override
    public String apply(Object value) {
        if (value == null) {
            return "";
        }
        
        if (value instanceof Date) {
            return DateUtil.format((Date) value, "yyyy-MM-dd");
        }
        
        if (value instanceof String) {
            String dateStr = (String) value;
            if (StrUtil.isNotEmpty(dateStr)) {
                try {
                    // 尝试解析日期字符串
                    Date date = DateUtil.parse(dateStr);
                    return DateUtil.format(date, "yyyy-MM-dd");
                } catch (Exception e) {
                    // 如果已经是yyyy-MM-dd格式，直接返回
                    if (dateStr.matches("\\d{4}-\\d{2}-\\d{2}")) {
                        return dateStr;
                    }
                    log.warn("[apply][日期格式化失败，原值：{}]", dateStr, e);
                    return dateStr;
                }
            }
        }
        
        return value.toString();
    }
} 