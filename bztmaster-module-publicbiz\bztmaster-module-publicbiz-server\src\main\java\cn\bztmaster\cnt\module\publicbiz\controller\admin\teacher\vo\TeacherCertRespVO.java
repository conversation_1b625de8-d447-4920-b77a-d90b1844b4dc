package cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@Schema(description = "师资库 - 讲师资质 Response VO")
public class TeacherCertRespVO {
    private Long id;
    private Long teacherId;
    private String certType;
    private String certName;
    private String fileName;
    private String fileUrl;
    @Schema(description = "证件有效期开始时间")
    private Date validStartDate;
    @Schema(description = "证件有效期结束时间")
    private Date validEndDate;
} 