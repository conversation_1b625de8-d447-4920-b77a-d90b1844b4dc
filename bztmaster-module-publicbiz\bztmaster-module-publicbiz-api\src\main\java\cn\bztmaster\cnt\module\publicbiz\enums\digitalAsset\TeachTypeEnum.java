package cn.bztmaster.cnt.module.publicbiz.enums.digitalAsset;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数字资产课程授课方式枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TeachTypeEnum {

    ONLINE("线上授课", "在线学习课程"),
    OFFLINE("线下授课", "线下面授课程");

    /**
     * 授课方式
     */
    private final String type;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据类型获取枚举
     *
     * @param type 类型
     * @return 枚举
     */
    public static TeachTypeEnum getByType(String type) {
        if (type == null) {
            return null;
        }
        for (TeachTypeEnum value : TeachTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
