<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.digitalAsset.CourseAttachmentMapper">
    
    <resultMap id="BaseResultMap" type="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset.CourseAttachmentDO">
        <id property="id" column="id" />
        <result property="courseId" column="course_id" />
        <result property="attachmentName" column="attachment_name" />
        <result property="attachmentType" column="attachment_type" />
        <result property="fileUrl" column="file_url" />
        <result property="fileSize" column="file_size" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="creator" column="creator" />
        <result property="updater" column="updater" />
        <result property="deleted" column="deleted" />
        <result property="tenantId" column="tenant_id" />
    </resultMap>

    <!-- 根据课程ID查询附件列表 -->
    <select id="selectListByCourseId" resultMap="BaseResultMap">
        SELECT id, course_id, attachment_name, attachment_type, file_url, file_size, 
               create_time, update_time, creator, updater, deleted, tenant_id
        FROM publicbiz_course_attachment
        WHERE deleted = 0 AND course_id = #{courseId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据课程ID和附件名称查询（用于重复性校验） -->
    <select id="selectByCourseIdAndName" resultMap="BaseResultMap">
        SELECT id, course_id, attachment_name, attachment_type, file_url, file_size, 
               create_time, update_time, creator, updater, deleted, tenant_id
        FROM publicbiz_course_attachment
        WHERE deleted = 0 AND course_id = #{courseId} AND attachment_name = #{attachmentName}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
        LIMIT 1
    </select>

    <!-- 根据课程ID统计附件数量 -->
    <select id="countByCourseId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM publicbiz_course_attachment
        WHERE deleted = 0 AND course_id = #{courseId}
    </select>

    <!-- 根据附件类型统计数量 -->
    <select id="countByAttachmentType" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM publicbiz_course_attachment
        WHERE deleted = 0 AND attachment_type = #{attachmentType}
    </select>

    <!-- 批量删除课程下的所有附件 -->
    <update id="deleteByCourseId">
        UPDATE publicbiz_course_attachment 
        SET deleted = 1, update_time = NOW()
        WHERE course_id = #{courseId} AND deleted = 0
    </update>

    <!-- 根据文件URL查询附件 -->
    <select id="selectByFileUrl" resultMap="BaseResultMap">
        SELECT id, course_id, attachment_name, attachment_type, file_url, file_size, 
               create_time, update_time, creator, updater, deleted, tenant_id
        FROM publicbiz_course_attachment
        WHERE deleted = 0 AND file_url = #{fileUrl}
        LIMIT 1
    </select>

    <!-- 根据课程ID和附件类型查询附件列表 -->
    <select id="selectByCourseIdAndType" resultMap="BaseResultMap">
        SELECT id, course_id, attachment_name, attachment_type, file_url, file_size, 
               create_time, update_time, creator, updater, deleted, tenant_id
        FROM publicbiz_course_attachment
        WHERE deleted = 0 AND course_id = #{courseId} AND attachment_type = #{attachmentType}
        ORDER BY create_time DESC
    </select>

    <!-- 计算课程附件总大小 -->
    <select id="sumFileSizeByCourseId" resultType="java.lang.Long">
        SELECT IFNULL(SUM(file_size), 0)
        FROM publicbiz_course_attachment
        WHERE deleted = 0 AND course_id = #{courseId}
    </select>

</mapper>
