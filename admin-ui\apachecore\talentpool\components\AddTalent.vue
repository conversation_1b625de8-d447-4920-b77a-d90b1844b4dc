<template>
  <el-dialog :model-value="visible" title="新建人才档案" width="700px" :close-on-click-modal="false" @close="onClose">
    <div class="talent-create-step">
      <el-steps :active="createStep" align-center>
        <el-step title="核心身份" />
        <el-step title="教育背景" />
        <el-step title="实践与项目" />
        <el-step title="培训与技能" />
        <el-step title="认证与资质" />
        <el-step title="求职与工作" />
      </el-steps>
    </div>
    <div v-if="createStep === 1" class="talent-create-form">
      <div class="section-title">A. 核心用户表 (USER)</div>
      <el-form :model="createForm" label-width="80px" ref="createFormRef" class="talent-form-grid">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="createForm.name" placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="createForm.phone" placeholder="请输入11位手机号" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证" prop="idCard">
              <el-input v-model="createForm.idCard" placeholder="请输入18位身份证" maxlength="18" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="createForm.email" placeholder="请输入电子邮箱" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="createForm.gender" placeholder="请选择性别">
                <el-option label="男" value="男" />
                <el-option label="女" value="女" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出生日期" prop="birthday">
              <el-date-picker v-model="createForm.birthday" type="date" placeholder="年/月/日" style="width: 100%;" :disabled-date="disabledBirthdayDate" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="人才标签">
              <el-input
                v-model="createForm.tagsInput"
                placeholder="添加标签...（输入后按回车添加）"
                @keyup.enter="addTag"
                clearable
              />
              <div class="talent-tags-list">
                <el-tag
                  v-for="(tag, idx) in createForm.tags"
                  :key="tag"
                  closable
                  @close="removeTag(idx)"
                  type="info"
                  style="margin-right: 6px; margin-top: 6px;"
                >{{ tag }}</el-tag>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <div class="recommend-title">推荐标签:</div>
            <div class="recommend-tags">
              <el-tag
                v-for="tag in recommendTags"
                :key="tag"
                :type="createForm.tags.includes(tag) ? 'success' : 'default'"
                @click="addRecommendTag(tag)"
                class="recommend-tag"
              >{{ tag }}</el-tag>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div style="text-align:right; margin-top: 20px;">
        <el-button type="primary" @click="nextStep">下一步</el-button>
      </div>
    </div>
    <div v-else-if="createStep === 2" class="talent-create-form">
      <div class="section-title">B. 教育背景 (EDUCATION)</div>
      <el-form :model="educationForm" label-width="100px" ref="educationFormRef" class="talent-form-grid">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="毕业院校" prop="school">
              <el-input v-model="educationForm.school" placeholder="例如：北京大学" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专业" prop="major">
              <el-input v-model="educationForm.major" placeholder="例如：计算机科学与技术" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学位类型" prop="degreeType">
              <el-select v-model="educationForm.degreeType" placeholder="请选择">
                <el-option label="博士" value="博士" />
                <el-option label="硕士" value="硕士" />
                <el-option label="本科" value="本科" />
                <el-option label="大专" value="大专" />
                <el-option label="中专" value="中专" />
                <el-option label="高中及以下" value="高中及以下" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学业排名" prop="rank">
              <el-input v-model="educationForm.rank" placeholder="例如：Top 5%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="在校开始时间" prop="startTime">
              <el-date-picker v-model="educationForm.startTime" type="date" placeholder="年/月/日" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="在校结束时间" prop="endTime">
              <el-date-picker v-model="educationForm.endTime" type="date" placeholder="年/月/日" style="width: 100%;" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div style="text-align:right; margin-top: 20px;">
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" @click="nextStep">下一步</el-button>
      </div>
    </div>
    <div v-else-if="createStep === 3" class="talent-create-form">
      <div class="section-title">C. 实践与项目 (PRACTICE & PROJECT)</div>
      <el-form :model="practiceForm" label-width="110px" ref="practiceFormRef" class="talent-form-grid">
        <!-- 校内实践 -->
        <div style="font-weight:bold; margin-bottom:8px;">校内实践 (CAMPUS_PRACTICE)</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="实践名称" prop="practiceName">
              <el-select v-model="practiceForm.practiceName" placeholder="请选择实践名称">
                <el-option label="实践A" value="实践A" />
                <el-option label="实践B" value="实践B" />
                <el-option label="实践C" value="实践C" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组织方" prop="practiceOrg">
              <el-input v-model="practiceForm.practiceOrg" placeholder="" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实践开始时间" prop="practiceStart">
              <el-date-picker v-model="practiceForm.practiceStart" type="date" placeholder="年/月/日" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实践结束时间" prop="practiceEnd">
              <el-date-picker v-model="practiceForm.practiceEnd" type="date" placeholder="年/月/日" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="实践报告/总结" prop="practiceSummary">
              <el-input v-model="practiceForm.practiceSummary" type="textarea" :rows="2" placeholder="" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 实习经历 -->
        <div style="font-weight:bold; margin:16px 0 8px 0;">实习经历 (INTERNSHIP)</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="实习公司" prop="internCompany">
              <el-select v-model="practiceForm.internCompany" placeholder="请选择实习公司">
                <el-option label="公司A" value="公司A" />
                <el-option label="公司B" value="公司B" />
                <el-option label="公司C" value="公司C" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实习岗位" prop="internJob">
              <el-input v-model="practiceForm.internJob" placeholder="" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实习开始时间" prop="internStart">
              <el-date-picker v-model="practiceForm.internStart" type="date" placeholder="年/月/日" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实习结束时间" prop="internEnd">
              <el-date-picker v-model="practiceForm.internEnd" type="date" placeholder="年/月/日" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="工作职责" prop="internDuty">
              <el-input v-model="practiceForm.internDuty" type="textarea" :rows="2" placeholder="" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 项目经历 -->
        <div style="font-weight:bold; margin:16px 0 8px 0;">项目经历 (PROJECT)</div>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="项目名称" prop="projectName">
              <el-select v-model="practiceForm.projectName" placeholder="请选择项目名称">
                <el-option label="项目A" value="项目A" />
                <el-option label="项目B" value="项目B" />
                <el-option label="项目C" value="项目C" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="项目描述" prop="projectDesc">
              <el-input v-model="practiceForm.projectDesc" type="textarea" :rows="2" placeholder="" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div style="text-align:right; margin-top: 20px;">
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" @click="nextStep">下一步</el-button>
      </div>
    </div>
    <div v-else-if="createStep === 4" class="talent-create-form">
      <div class="section-title">D. 培训与技能 (TRAINING & SKILL)</div>
      <div style="font-weight:bold; margin-bottom:8px;">培训记录 (TRAINING)</div>
      <el-form :model="trainingForm" label-width="100px" ref="trainingFormRef" class="talent-form-grid">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="培训机构" prop="org">
              <el-input v-model="trainingForm.org" placeholder="例如：汇成平台" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="课程名称" prop="course">
              <el-input v-model="trainingForm.course" placeholder="例如：金牌月嫂培训" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="完成日期" prop="finishDate">
              <el-date-picker v-model="trainingForm.finishDate" type="date" placeholder="年/月/日" style="width: 100%;" />
            </el-form-item>
          </el-col>
        </el-row>
        <div style="font-weight:bold; margin:16px 0 8px 0;">技能清单 (SKILL)</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="技能名称" prop="skillName">
              <el-input v-model="trainingForm.skillName" placeholder="例如：深度保洁" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="掌握程度" prop="skillLevel">
              <el-select v-model="trainingForm.skillLevel" placeholder="请选择掌握程度">
                <el-option label="了解" value="了解" />
                <el-option label="熟悉" value="熟悉" />
                <el-option label="精通" value="精通" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div style="text-align:right; margin-top: 20px;">
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" @click="nextStep">下一步</el-button>
      </div>
    </div>
    <div v-else-if="createStep === 5" class="talent-create-form">
      <div class="section-title">E. 认证与资质 (CERTIFICATE)</div>
      <el-form :model="certificateForm" label-width="100px" ref="certificateFormRef" class="talent-form-grid">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="证书名称" prop="certName">
              <el-input v-model="certificateForm.certName" placeholder="例如：高级母婴护理师证" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证书编号" prop="certNo">
              <el-input v-model="certificateForm.certNo" placeholder="请输入证书编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发证机构" prop="certOrg">
              <el-input v-model="certificateForm.certOrg" placeholder="例如：XX市人社局" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="颁发日期" prop="issueDate">
              <el-date-picker v-model="certificateForm.issueDate" type="date" placeholder="年/月/日" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="到期时间" prop="expireDate">
              <el-date-picker v-model="certificateForm.expireDate" type="date" placeholder="年/月/日" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="记录来源" prop="source">
              <el-select v-model="certificateForm.source" placeholder="请选择记录来源">
                <el-option label="平台录入" value="平台录入" />
                <el-option label="机构录入" value="机构录入" />
                <el-option label="个人申报" value="个人申报" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审核状态" prop="status">
              <el-select v-model="certificateForm.status" placeholder="请选择审核状态">
                <el-option label="待认证" value="待认证" />
                <el-option label="已认证" value="已认证" />
                <el-option label="已驳回" value="已驳回" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="证书图片" prop="file">
              <el-upload
                class="upload-demo"
                action="#"
                :auto-upload="false"
                :show-file-list="true"
                :limit="1"
                :on-change="handleCertFileChange"
              >
                <el-button>选择文件</el-button>
                <template #tip>
                  <div style="color: #888; margin-left: 10px; display: inline-block;">未选择任何文件</div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div style="text-align:right; margin-top: 20px;">
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" @click="nextStep">下一步</el-button>
      </div>
    </div>
    <div v-else-if="createStep === 6" class="talent-create-form">
      <div class="section-title">F. 求职与工作履历 (APPLICATION & EMPLOYMENT)</div>
      <el-form :model="jobForm" label-width="110px" ref="jobFormRef" class="talent-form-grid">
        <!-- 工作履历 -->
        <div style="font-weight:bold; margin-bottom:8px;">工作履历 (EMPLOYMENT)</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="就业公司" prop="company">
              <el-input v-model="jobForm.company" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职位" prop="position">
              <el-input v-model="jobForm.position" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任职开始时间" prop="startTime">
              <el-date-picker v-model="jobForm.startTime" type="date" placeholder="年/月/日" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任职结束时间" prop="endTime">
              <el-date-picker v-model="jobForm.endTime" type="date" placeholder="年/月/日" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="薪资 (月薪, 元)" prop="salary">
              <el-input v-model="jobForm.salary" placeholder="例如: 8000" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 求职记录 -->
        <div style="font-weight:bold; margin:16px 0 8px 0;">求职记录 (JOB_APPLICATION)</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请公司" prop="applyCompany">
              <el-input v-model="jobForm.applyCompany" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请职位" prop="applyPosition">
              <el-input v-model="jobForm.applyPosition" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请日期" prop="applyDate">
              <el-date-picker v-model="jobForm.applyDate" type="date" placeholder="年/月/日" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="applyStatus">
              <el-select v-model="jobForm.applyStatus" placeholder="请选择状态">
                <el-option label="已投递" value="已投递" />
                <el-option label="面试中" value="面试中" />
                <el-option label="已录用" value="已录用" />
                <el-option label="不合适" value="不合适" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div style="text-align:right; margin-top: 20px;">
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" @click="onSave">保存</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineProps, defineEmits, watch } from 'vue'
const props = defineProps<{ modelValue: boolean, talent?: any }>()
const emit = defineEmits(['update:modelValue', 'saved'])

const visible = computed({
  get: () => props.modelValue,
  set: (val: boolean) => emit('update:modelValue', val)
})

const createStep = ref(1)
const createForm = reactive({
  name: '',
  phone: '',
  idCard: '',
  email: '',
  gender: '男',
  birthday: '',
  tagsInput: '',
  tags: [] as string[],
})
const createFormRef = ref()
const educationForm = reactive({
  school: '',
  major: '',
  degreeType: '',
  rank: '',
  startTime: '',
  endTime: ''
})
const educationFormRef = ref()
const practiceForm = reactive({
  practiceName: '',
  practiceOrg: '',
  practiceStart: '',
  practiceEnd: '',
  practiceSummary: '',
  internCompany: '',
  internJob: '',
  internStart: '',
  internEnd: '',
  internDuty: '',
  projectName: '',
  projectDesc: ''
})
const practiceFormRef = ref()
const recommendTags = [
  '认证:高级母婴护理', '认证:金牌月嫂', '认证:健康证', '认证:教师资格证', '技能:催乳', '技能:高级收纳',
  '技能:康复保健', '技能:Office办公', '技能:沟通能力强', '经验:育儿早', '经验:服务高端社区',
  '经验:2年+工作经验', '经验:服务30+家庭', '评价:细心', '评价:有耐心', '评价:守时',
  '学历:博士', '学历:硕士', '学历:985', '学历:本科', '学历:大学', '身份:学生', '身份:认证月嫂',
  '身份:培训学员', '身份:求职者', '已实名'
]
const trainingForm = reactive({
  org: '',
  course: '',
  finishDate: '',
  skillName: '',
  skillLevel: ''
})
const trainingFormRef = ref()
const certificateForm = reactive({
  certName: '',
  certNo: '',
  certOrg: '',
  issueDate: '',
  expireDate: '',
  source: '平台录入',
  status: '待认证',
  file: null as File | null
})
const certificateFormRef = ref()
const jobForm = reactive({
  company: '',
  position: '',
  startTime: '',
  endTime: '',
  salary: '',
  applyCompany: '',
  applyPosition: '',
  applyDate: '',
  applyStatus: '已投递'
})
const jobFormRef = ref()

watch(() => props.talent, (val) => {
  if (val && visible.value) {
    createForm.name = val.name || ''
    createForm.phone = val.phone || ''
    createForm.idCard = val.idCard || ''
    createForm.email = val.email || ''
    createForm.gender = val.gender || '男'
    createForm.birthday = val.birthday || ''
    createForm.tags = val.tags ? [...val.tags] : []
    // 其它表单项可根据需要补充
  } else if (!val && visible.value) {
    // 新建时清空
    createForm.name = ''
    createForm.phone = ''
    createForm.idCard = ''
    createForm.email = ''
    createForm.gender = '男'
    createForm.birthday = ''
    createForm.tags = []
  }
})

watch(visible, (val) => {
  if (!val) {
    // 关闭弹窗时重置步骤
    createStep.value = 1
  }
})

function addTag() {
  const val = createForm.tagsInput.trim()
  if (val && !createForm.tags.includes(val)) {
    createForm.tags.push(val)
  }
  createForm.tagsInput = ''
}
function removeTag(idx: number) {
  createForm.tags.splice(idx, 1)
}
function addRecommendTag(tag: string) {
  if (!createForm.tags.includes(tag)) {
    createForm.tags.push(tag)
  }
}
function nextStep() {
  if (createStep.value < 6) {
    createStep.value++
  }
}
function prevStep() {
  if (createStep.value > 1) {
    createStep.value--
  }
}
function disabledBirthdayDate(date: Date) {
  return date.getTime() >= Date.now();
}
function handleCertFileChange(file) {
  certificateForm.file = file.raw
}
function onSave() {
  emit('saved')
  emit('update:modelValue', false)
}
function onClose() {
  emit('update:modelValue', false)
}
</script>

<style scoped>
.talent-create-step {
  margin-bottom: 24px;
}
.talent-create-form {
  margin-top: 10px;
}
.section-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
}
.talent-form-grid .el-form-item {
  margin-bottom: 18px;
}
.talent-tags-list {
  margin-top: 6px;
}
.recommend-title {
  font-weight: bold;
  margin-bottom: 6px;
  margin-top: 10px;
}
.recommend-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px 8px;
}
.recommend-tag {
  cursor: pointer;
  margin-bottom: 6px;
  user-select: none;
}
.recommend-tag:hover {
  background: #f0f9eb;
  color: #67c23a;
}
</style>
