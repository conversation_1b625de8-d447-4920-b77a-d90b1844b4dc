package cn.bztmaster.cnt.module.publicbiz.controller.admin.leads.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 线索信息保存 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 线索信息保存 Request VO")
@Data
public class LeadsSaveReqVO {

    @Schema(description = "主键，更新时必传", example = "1024")
    private Long id;

    @Schema(description = "线索ID，系统生成的唯一标识，新增时可不传", example = "LEAD20250721001")
    private String leadId;

    @Schema(description = "客户姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "客户姓名不能为空")
    @Size(max = 100, message = "客户姓名长度不能超过100")
    private String customerName;

    @Schema(description = "联系电话，11位手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "13800138000")
    @NotEmpty(message = "联系电话不能为空")
    @Size(max = 20, message = "联系电话长度不能超过20")
    private String customerPhone;

    @Schema(description = "线索来源", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "线索来源不能为空")
    private Integer leadSource;

    @Schema(description = "业务模块", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "业务模块不能为空")
    private Integer businessModule;

    @Schema(description = "线索状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "线索状态不能为空")
    private Integer leadStatus;

    @Schema(description = "创建方式", example = "1")
    private Integer createMethod;

    @Schema(description = "当前跟进人ID", example = "1024")
    @Size(max = 64, message = "当前跟进人ID长度不能超过64")
    private String currentOwner;

    @Schema(description = "当前跟进人姓名", example = "李四")
    @Size(max = 64, message = "当前跟进人姓名长度不能超过64")
    private String currentOwnerName;

    @Schema(description = "备注信息", example = "客户对产品很感兴趣")
    private String remark;
}