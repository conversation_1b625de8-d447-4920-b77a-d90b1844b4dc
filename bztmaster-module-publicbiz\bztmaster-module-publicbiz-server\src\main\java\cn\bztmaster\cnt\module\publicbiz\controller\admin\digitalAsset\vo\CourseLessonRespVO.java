package cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

/**
 * 课程课时 - Response VO
 * 对应前端页面：src/views/infra/ResourceCenter/DigitalAsset/components/ManagementCourseForOnline.vue
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "课程课时 - Response VO")
public class CourseLessonRespVO {

    @Schema(description = "课时ID", example = "1")
    private Long id;

    @Schema(description = "课程ID", example = "123")
    private Long courseId;

    @Schema(description = "章节ID", example = "1001")
    private Long chapterId;

    @Schema(description = "课时标题", example = "课时1：SWOT分析法概述")
    private String title;

    @Schema(description = "课时类型", example = "视频")
    private String lessonType;

    @Schema(description = "是否免费试看", example = "true")
    private Boolean isFree;

    @Schema(description = "关联素材ID", example = "material001")
    private String materialId;

    @Schema(description = "关联素材名称", example = "课程导论.mp4")
    private String materialName;

    @Schema(description = "关联素材文件URL", example = "https://example.com/video1.mp4")
    private String materialFileUrl;

    @Schema(description = "排序序号", example = "1")
    private Integer sortOrder;

    @Schema(description = "创建时间")
    private Date createTime;
}
