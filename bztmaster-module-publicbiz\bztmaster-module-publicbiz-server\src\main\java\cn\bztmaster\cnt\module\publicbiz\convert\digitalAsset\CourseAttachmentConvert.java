package cn.bztmaster.cnt.module.publicbiz.convert.digitalAsset;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset.CourseAttachmentDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
 * 课程附件 Convert
 * 
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface CourseAttachmentConvert {
    
    CourseAttachmentConvert INSTANCE = Mappers.getMapper(CourseAttachmentConvert.class);

    /**
     * VO 转 DO
     */
    CourseAttachmentDO convert(CourseAttachmentSaveReqVO bean);

    /**
     * DO 转 VO
     */
    CourseAttachmentRespVO convert(CourseAttachmentDO bean);

    /**
     * DO 列表转 VO 列表
     */
    List<CourseAttachmentRespVO> convertList(List<CourseAttachmentDO> list);
}
