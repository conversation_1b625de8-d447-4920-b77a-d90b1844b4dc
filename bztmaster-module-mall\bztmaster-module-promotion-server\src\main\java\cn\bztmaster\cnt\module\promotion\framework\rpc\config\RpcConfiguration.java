package cn.bztmaster.cnt.module.promotion.framework.rpc.config;

import cn.bztmaster.cnt.module.infra.api.websocket.WebSocketSenderApi;
import cn.bztmaster.cnt.module.member.api.user.MemberUserApi;
import cn.bztmaster.cnt.module.product.api.category.ProductCategoryApi;
import cn.bztmaster.cnt.module.product.api.sku.ProductSkuApi;
import cn.bztmaster.cnt.module.product.api.spu.ProductSpuApi;
import cn.bztmaster.cnt.module.system.api.social.SocialClientApi;
import cn.bztmaster.cnt.module.system.api.user.AdminUserApi;
import cn.bztmaster.cnt.module.trade.api.order.TradeOrderApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(value = "promotionRpcConfiguration", proxyBeanMethods = false)
@EnableFeignClients(clients = {ProductSkuApi.class, ProductSpuApi.class, ProductCategoryApi.class,
        MemberUserApi.class, TradeOrderApi.class, AdminUserApi.class, SocialClientApi.class,
        WebSocketSenderApi.class})
public class RpcConfiguration {
}
