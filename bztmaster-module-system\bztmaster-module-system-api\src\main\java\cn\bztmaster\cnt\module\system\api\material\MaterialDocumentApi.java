package cn.bztmaster.cnt.module.system.api.material;

import cn.hutool.core.convert.Convert;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.util.collection.CollectionUtils;
import cn.bztmaster.cnt.module.system.api.material.dto.DocumentRespDTO;
import cn.bztmaster.cnt.module.system.enums.ApiConstants;
import com.fhs.core.trans.anno.AutoTrans;
import com.fhs.trans.service.AutoTransable;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.*;

import static cn.bztmaster.cnt.module.system.api.material.MaterialDocumentApi.PREFIX;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 文档素材")
@AutoTrans(namespace = PREFIX, fields = {"documentName"})
public interface MaterialDocumentApi extends AutoTransable<DocumentRespDTO> {

    String PREFIX = ApiConstants.PREFIX + "/material/document";

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "通过文档 ID 查询文档")
    @Parameter(name = "id", description = "文档编号", example = "1", required = true)
    CommonResult<DocumentRespDTO> getDocument(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "通过文档 ID 查询文档们")
    @Parameter(name = "ids", description = "文档编号数组", example = "1,2", required = true)
    CommonResult<List<DocumentRespDTO>> getDocumentList(@RequestParam("ids") Collection<Long> ids);

    @GetMapping(PREFIX + "/list-by-category")
    @Operation(summary = "通过分类 ID 查询文档们")
    @Parameter(name = "categoryId", description = "分类编号", example = "1", required = true)
    CommonResult<List<DocumentRespDTO>> getDocumentListByCategoryId(@RequestParam("categoryId") Long categoryId);

    @GetMapping(PREFIX + "/list-by-org")
    @Operation(summary = "通过来源机构 ID 查询文档们")
    @Parameter(name = "sourceOrgId", description = "来源机构编号", example = "1", required = true)
    CommonResult<List<DocumentRespDTO>> getDocumentListBySourceOrgId(@RequestParam("sourceOrgId") Long sourceOrgId);

    @GetMapping(PREFIX + "/recycleList")
    @Operation(summary = "文档回收站列表")
    CommonResult<List<DocumentRespDTO>> getDocumentRecycleList(@RequestParam Map<String, Object> params);

    @PostMapping(PREFIX + "/recycleRestore")
    @Operation(summary = "文档回收站恢复")
    CommonResult<Boolean> recycleRestore(@RequestBody List<Long> idList);

    @PostMapping(PREFIX + "/recycleDelete")
    @Operation(summary = "文档回收站永久删除")
    CommonResult<Boolean> recycleDelete(@RequestBody List<Long> idList);

    /**
     * 获得文档 Map
     *
     * @param ids 文档编号数组
     * @return 文档 Map
     */
    default Map<Long, DocumentRespDTO> getDocumentMap(Collection<Long> ids) {
        List<DocumentRespDTO> documents = getDocumentList(ids).getCheckedData();
        return CollectionUtils.convertMap(documents, DocumentRespDTO::getId);
    }

    /**
     * 校验文档是否有效。如下情况，视为无效：
     * 1. 文档编号不存在
     * 2. 文档被禁用
     *
     * @param id 文档编号
     */
    default void validateDocument(Long id) {
        validateDocumentList(Collections.singleton(id));
    }

    @GetMapping(PREFIX + "/valid")
    @Operation(summary = "校验文档们是否有效")
    @Parameter(name = "ids", description = "文档编号数组", example = "3,5", required = true)
    CommonResult<Boolean> validateDocumentList(@RequestParam("ids") Collection<Long> ids);

    @Override
    @GetMapping("select")
    default List<DocumentRespDTO> selectByIds(List<?> ids) {
        return getDocumentList(Convert.toList(Long.class, ids)).getCheckedData();
    }

    @Override
    @GetMapping("select-list")
    default DocumentRespDTO selectById(Object id) {
        return getDocument(Convert.toLong(id)).getCheckedData();
    }
} 