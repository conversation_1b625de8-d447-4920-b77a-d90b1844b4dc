package cn.bztmaster.cnt.module.system.api.material;

import cn.hutool.core.convert.Convert;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.util.collection.CollectionUtils;
import cn.bztmaster.cnt.module.system.api.material.dto.ArticleRespDTO;
import cn.bztmaster.cnt.module.system.enums.ApiConstants;
import com.fhs.core.trans.anno.AutoTrans;
import com.fhs.trans.service.AutoTransable;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.*;

import static cn.bztmaster.cnt.module.system.api.material.MaterialArticleApi.PREFIX;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 文章素材")
@AutoTrans(namespace = PREFIX, fields = {"title"})
public interface MaterialArticleApi extends AutoTransable<ArticleRespDTO> {

    String PREFIX = ApiConstants.PREFIX + "/material/article";

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "通过文章 ID 查询文章")
    @Parameter(name = "id", description = "文章编号", example = "1", required = true)
    CommonResult<ArticleRespDTO> getArticle(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "通过文章 ID 查询文章们")
    @Parameter(name = "ids", description = "文章编号数组", example = "1,2", required = true)
    CommonResult<List<ArticleRespDTO>> getArticleList(@RequestParam("ids") Collection<Long> ids);

    @GetMapping(PREFIX + "/list-by-category")
    @Operation(summary = "通过分类 ID 查询文章们")
    @Parameter(name = "categoryId", description = "分类编号", example = "1", required = true)
    CommonResult<List<ArticleRespDTO>> getArticleListByCategoryId(@RequestParam("categoryId") Long categoryId);

    @GetMapping(PREFIX + "/list-by-org")
    @Operation(summary = "通过来源机构 ID 查询文章们")
    @Parameter(name = "sourceOrgId", description = "来源机构编号", example = "1", required = true)
    CommonResult<List<ArticleRespDTO>> getArticleListBySourceOrgId(@RequestParam("sourceOrgId") Long sourceOrgId);

    @GetMapping(PREFIX + "/recycleList")
    @Operation(summary = "文章回收站列表")
    CommonResult<List<ArticleRespDTO>> getArticleRecycleList(@RequestParam Map<String, Object> params);

    @PostMapping(PREFIX + "/recycleRestore")
    @Operation(summary = "文章回收站恢复")
    CommonResult<Boolean> recycleRestore(@RequestBody List<Long> idList);

    @PostMapping(PREFIX + "/recycleDelete")
    @Operation(summary = "文章回收站永久删除")
    CommonResult<Boolean> recycleDelete(@RequestBody List<Long> idList);

    /**
     * 获得文章 Map
     *
     * @param ids 文章编号数组
     * @return 文章 Map
     */
    default Map<Long, ArticleRespDTO> getArticleMap(Collection<Long> ids) {
        List<ArticleRespDTO> articles = getArticleList(ids).getCheckedData();
        return CollectionUtils.convertMap(articles, ArticleRespDTO::getId);
    }

    /**
     * 校验文章是否有效。如下情况，视为无效：
     * 1. 文章编号不存在
     * 2. 文章被禁用
     *
     * @param id 文章编号
     */
    default void validateArticle(Long id) {
        validateArticleList(Collections.singleton(id));
    }

    @GetMapping(PREFIX + "/valid")
    @Operation(summary = "校验文章们是否有效")
    @Parameter(name = "ids", description = "文章编号数组", example = "3,5", required = true)
    CommonResult<Boolean> validateArticleList(@RequestParam("ids") Collection<Long> ids);

    @Override
    @GetMapping("select")
    default List<ArticleRespDTO> selectByIds(List<?> ids) {
        return getArticleList(Convert.toList(Long.class, ids)).getCheckedData();
    }

    @Override
    @GetMapping("select-list")
    default ArticleRespDTO selectById(Object id) {
        return getArticle(Convert.toLong(id)).getCheckedData();
    }
} 