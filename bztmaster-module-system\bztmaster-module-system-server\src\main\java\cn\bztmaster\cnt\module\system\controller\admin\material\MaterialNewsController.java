package cn.bztmaster.cnt.module.system.controller.admin.material;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.service.material.MaterialNewsService;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/system/material/news")
@Tag(name = "素材库-图文管理")
@Slf4j
@Validated
public class MaterialNewsController {
    @Resource
    private MaterialNewsService newsService;

    @GetMapping("/list")
    @Operation(summary = "图文列表")
    public CommonResult<PageResult<NewsRespVO>> list(@Validated NewsPageReqVO reqVO) {
        return CommonResult.success(newsService.getNewsPage(reqVO));
    }

    @PostMapping("/create")
    @Operation(summary = "新增图文")
    public CommonResult<Long> create(@RequestBody @Validated NewsSaveReqVO reqVO) {
        return CommonResult.success(newsService.createNews(reqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "编辑图文")
    public CommonResult<Boolean> update(@RequestBody @Validated NewsSaveReqVO reqVO) {
        newsService.updateNews(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除图文")
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        newsService.deleteNews(id);
        return CommonResult.success(true);
    }

    @GetMapping("/detail")
    @Operation(summary = "图文详情")
    public CommonResult<NewsRespVO> detail(@RequestParam("id") Long id) {
        return CommonResult.success(newsService.getNewsDetail(id));
    }

    @GetMapping("/recycleList")
    @Operation(summary = "图文回收站列表")
    public CommonResult<PageResult<NewsRespVO>> recycleList(@Validated NewsPageReqVO reqVO,
                                                          @RequestParam(value = "updateTimeFrom", required = false) String updateTimeFrom,
                                                          @RequestParam(value = "updateTimeTo", required = false) String updateTimeTo) {
        return CommonResult.success(newsService.getNewsRecyclePage(reqVO, updateTimeFrom, updateTimeTo));
    }

    @PostMapping("/recycleRestore")
    @Operation(summary = "图文回收站恢复")
    public CommonResult<Boolean> recycleRestore(@RequestBody List<Long> idList) {
        newsService.restoreNewsFromRecycle(idList);
        return CommonResult.success(true);
    }

    @PostMapping("/recycleDelete")
    @Operation(summary = "图文回收站永久删除")
    public CommonResult<Boolean> recycleDelete(@RequestBody List<Long> idList) {
        newsService.deleteNewsFromRecycle(idList);
        return CommonResult.success(true);
    }
} 