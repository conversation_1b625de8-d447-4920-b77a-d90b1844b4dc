package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.teacher;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("publicbiz_teacher")
@Schema(description = "师资库主表 DO")
public class TeacherDO {
    @TableId
    private Long id;
    private String avatar;
    private String name;
    private String description;
    private String type;
    private String biz;
    private String orgId;
    private String org;
    private String field;
    private String phone;
    private String email;
    private String status;
    private String signStatus;
    private Date signDate;
    private String contractType;
    private String contractTemplate;
    private String contractNo;
    private String contractName;
    private Date contractPeriodStart;
    private Date contractPeriodEnd;
    private BigDecimal contractAmount;
    private String contractFileName;
    private String contractFileUrl;
    private Date createTime;
    private Date updateTime;
    private String creator;
    private String updater;
    private Boolean deleted;
    private Long tenantId;
} 