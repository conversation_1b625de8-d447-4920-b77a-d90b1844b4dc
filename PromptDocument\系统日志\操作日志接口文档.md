# 操作日志接口文档

## 接口概述

操作日志接口用于查询系统中的操作日志记录，支持分页查询和多种筛选条件。

## 接口列表

### 1. 查看操作日志分页列表

#### 接口信息
- **接口路径**: `/system/operate-log/page`
- **请求方式**: `GET`
- **接口描述**: 查看操作日志分页列表，支持按用户、模块、操作类型等条件筛选
- **权限要求**: `system:operate-log:query`

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| pageNo | Integer | 是 | 页码，从1开始 | 1 |
| pageSize | Integer | 是 | 每页条数，最大值为100 | 10 |
| userId | Long | 否 | 用户编号 | 1024 |
| bizId | Long | 否 | 操作模块业务编号 | 1 |
| type | String | 否 | 操作模块，模糊匹配 | "订单" |
| subType | String | 否 | 操作名，模糊匹配 | "创建订单" |
| action | String | 否 | 操作明细，模糊匹配 | "修改编号为1的用户信息" |
| createTime | LocalDateTime[] | 否 | 开始时间，格式：yyyy-MM-dd HH:mm:ss | ["2022-07-01 00:00:00", "2022-07-01 23:59:59"] |

#### 响应参数

| 参数名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| list | Array | 数据列表 | - |
| list[].id | Long | 日志编号 | 1024 |
| list[].traceId | String | 链路追踪编号 | "89aca178-a370-411c-ae02-3f0d672be4ab" |
| list[].userId | Long | 用户编号 | 1024 |
| list[].userName | String | 用户昵称 | "芋艿" |
| list[].type | String | 操作模块类型 | "订单" |
| list[].subType | String | 操作名 | "创建订单" |
| list[].bizId | Long | 操作模块业务编号 | 1 |
| list[].action | String | 操作明细 | "修改编号为1的用户信息，将性别从男改成女，将姓名从芋道改成源码。" |
| list[].extra | String | 拓展字段 | "{'orderId': 1}" |
| list[].requestMethod | String | 请求方法名 | "GET" |
| list[].requestUrl | String | 请求地址 | "/xxx/yyy" |
| list[].userIp | String | 用户IP | "127.0.0.1" |
| list[].userAgent | String | 浏览器UserAgent | "Mozilla/5.0" |
| list[].createTime | LocalDateTime | 创建时间 | "2022-07-01 10:00:00" |
| total | Long | 总记录数 | 100 |

#### 请求示例

```bash
GET /system/operate-log/page?pageNo=1&pageSize=10&type=PARTNER 合作伙伴&bizId=5
```

#### 响应示例

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 9093,
        "traceId": "",
        "userId": 1,
        "userName": "系统管理员",
        "type": "PARTNER 合作伙伴",
        "subType": "更新合作伙伴",
        "bizId": 5,
        "action": "更新了合作伙伴【AAABB机构】: 【业务模块】从【已终止】修改为【待审核】；【合同结束日期】从【Wed Jul 15 00:00:00 CST 2026】修改为【2026-07-15】；【合同开始日期】从【Mon Feb 06 00:00:00 CST 2023】修改为【2023-02-06】；【成立日期】从【Mon Jan 27 00:00:00 CST 2020】修改为【2020-01-27】；【续约提醒日期】从【Tue Jun 30 00:00:00 CST 2026】修改为【2026-06-30】；【合作状态】从【已终止】修改为【待审核】",
        "extra": "",
        "requestMethod": "POST",
        "requestUrl": "/admin-api/publicbiz/partner/update",
        "userIp": "0:0:0:0:0:0:0:1",
        "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "createTime": 1753163432000,
        "transMap": {}
      },
      {
        "id": 9092,
        "traceId": "",
        "userId": 1,
        "userName": "系统管理员",
        "type": "PARTNER 合作伙伴",
        "subType": "更新合作伙伴",
        "bizId": 5,
        "action": "更新了合作伙伴【AAA机构】: 【合同结束日期】从【Wed Jul 15 00:00:00 CST 2026】修改为【2026-07-15】；【合同开始日期】从【Mon Feb 06 00:00:00 CST 2023】修改为【2023-02-06】；【成立日期】从【Mon Jan 27 00:00:00 CST 2020】修改为【2020-01-27】；【机构名称】从【AAA机构】修改为【AAABB机构】；【续约提醒日期】从【Tue Jun 30 00:00:00 CST 2026】修改为【2026-06-30】",
        "extra": "",
        "requestMethod": "POST",
        "requestUrl": "/admin-api/publicbiz/partner/update",
        "userIp": "0:0:0:0:0:0:0:1",
        "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "createTime": 1753163350000,
        "transMap": {}
      }
    ],
    "total": 2
  },
  "msg": ""
}

```

#### 错误码说明

| 错误码 | 描述 |
|--------|------|
| 0 | 成功 |
| 1001 | 参数校验失败 |
| 1002 | 权限不足 |

### 2. 导出操作日志

#### 接口信息
- **接口路径**: `/system/operate-log/export`
- **请求方式**: `GET`
- **接口描述**: 导出操作日志到Excel文件
- **权限要求**: `system:operate-log:export`

#### 请求参数

与分页查询接口相同，但会忽略分页参数，导出所有符合条件的数据。

#### 响应

直接返回Excel文件流，文件名：`操作日志.xls`

## 使用说明

### 1. 分页查询

- `pageNo`: 页码从1开始
- `pageSize`: 每页条数，最大值为100
- 当`pageSize`设置为-1时，表示不分页，查询所有数据

### 2. 筛选条件

- **用户筛选**: 通过`userId`筛选特定用户的操作日志
- **模块筛选**: 通过`type`筛选特定模块的操作日志
- **操作筛选**: 通过`subType`筛选特定操作类型的日志
- **内容筛选**: 通过`action`筛选包含特定内容的操作日志
- **时间筛选**: 通过`createTime`数组筛选时间范围内的日志

### 3. 时间格式

时间参数使用`yyyy-MM-dd HH:mm:ss`格式，例如：
```
createTime=["2022-07-01 00:00:00", "2022-07-01 23:59:59"]
```

### 4. 权限控制

- 查询操作日志需要`system:operate-log:query`权限
- 导出操作日志需要`system:operate-log:export`权限

## 前端集成示例

### JavaScript/TypeScript 示例

```typescript
// 查询操作日志
async function getOperateLogs(params: {
  pageNo: number;
  pageSize: number;
  userId?: number;
  type?: string;
  subType?: string;
  action?: string;
  createTime?: string[];
}) {
  const response = await fetch('/system/operate-log/page?' + new URLSearchParams(params));
  const result = await response.json();
  return result.data;
}

// 导出操作日志
async function exportOperateLogs(params: {
  userId?: number;
  type?: string;
  subType?: string;
  action?: string;
  createTime?: string[];
}) {
  const response = await fetch('/system/operate-log/export?' + new URLSearchParams(params));
  const blob = await response.blob();
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = '操作日志.xls';
  a.click();
  window.URL.revokeObjectURL(url);
}
```

### Vue.js 示例

```vue
<template>
  <div>
    <!-- 查询表单 -->
    <el-form :model="queryForm" inline>
      <el-form-item label="用户">
        <el-input v-model="queryForm.userId" placeholder="用户编号" />
      </el-form-item>
      <el-form-item label="模块">
        <el-input v-model="queryForm.type" placeholder="操作模块" />
      </el-form-item>
      <el-form-item label="操作">
        <el-input v-model="queryForm.subType" placeholder="操作名" />
      </el-form-item>
      <el-form-item label="时间范围">
        <el-date-picker
          v-model="queryForm.createTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table :data="tableData" border>
      <el-table-column prop="id" label="日志编号" width="80" />
      <el-table-column prop="userName" label="操作人" width="120" />
      <el-table-column prop="type" label="操作模块" width="120" />
      <el-table-column prop="subType" label="操作名" width="120" />
      <el-table-column prop="action" label="操作明细" />
      <el-table-column prop="userIp" label="IP地址" width="120" />
      <el-table-column prop="createTime" label="操作时间" width="180" />
    </el-table>

    <!-- 分页 -->
    <el-pagination
      :current-page="pagination.pageNo"
      :page-size="pagination.pageSize"
      :total="pagination.total"
      @current-change="handlePageChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      queryForm: {
        pageNo: 1,
        pageSize: 10,
        userId: null,
        type: '',
        subType: '',
        action: '',
        createTime: []
      },
      tableData: [],
      pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  methods: {
    async handleQuery() {
      try {
        const response = await this.$http.get('/system/operate-log/page', {
          params: this.queryForm
        });
        this.tableData = response.data.list;
        this.pagination.total = response.data.total;
      } catch (error) {
        this.$message.error('查询失败');
      }
    },
    async handleExport() {
      try {
        const response = await this.$http.get('/system/operate-log/export', {
          params: this.queryForm,
          responseType: 'blob'
        });
        const blob = new Blob([response.data]);
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = '操作日志.xls';
        a.click();
        window.URL.revokeObjectURL(url);
      } catch (error) {
        this.$message.error('导出失败');
      }
    },
    handlePageChange(page) {
      this.queryForm.pageNo = page;
      this.handleQuery();
    },
    handleSizeChange(size) {
      this.queryForm.pageSize = size;
      this.queryForm.pageNo = 1;
      this.handleQuery();
    }
  },
  mounted() {
    this.handleQuery();
  }
}
</script>
```

## 注意事项

1. **权限控制**: 确保用户具有相应的操作权限
2. **参数校验**: 注意参数格式和必填项
3. **时间格式**: 时间参数必须使用正确的格式
4. **分页限制**: 每页条数不能超过100
5. **导出限制**: 导出功能会忽略分页参数，导出所有符合条件的数据
6. **性能考虑**: 大量数据查询时建议使用时间范围筛选
