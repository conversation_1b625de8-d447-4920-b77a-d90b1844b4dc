package cn.bztmaster.cnt.module.publicbiz.enums.digitalAsset;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数字资产课程分类枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CourseCategoryEnum {

    DOMESTIC_SKILLS("家政技能", "家政服务相关技能培训"),
    PROFESSIONAL_QUALITY("职业素养", "职业发展和素养提升"),
    UNIVERSITY_PRACTICE("高校实践", "高校学生实践课程"),
    ENTERPRISE_MANAGEMENT("企业管理", "企业管理相关课程");

    /**
     * 分类值
     */
    private final String category;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据分类值获取枚举
     *
     * @param category 分类值
     * @return 枚举
     */
    public static CourseCategoryEnum getByCategory(String category) {
        if (category == null) {
            return null;
        }
        for (CourseCategoryEnum value : CourseCategoryEnum.values()) {
            if (value.getCategory().equals(category)) {
                return value;
            }
        }
        return null;
    }
}
