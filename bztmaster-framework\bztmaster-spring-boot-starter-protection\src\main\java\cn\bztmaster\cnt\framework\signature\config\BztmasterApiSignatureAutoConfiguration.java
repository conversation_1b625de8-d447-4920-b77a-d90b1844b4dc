package cn.bztmaster.cnt.framework.signature.config;

import cn.bztmaster.cnt.framework.redis.config.BztmasterRedisAutoConfiguration;
import cn.bztmaster.cnt.framework.signature.core.aop.ApiSignatureAspect;
import cn.bztmaster.cnt.framework.signature.core.redis.ApiSignatureRedisDAO;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * HTTP API 签名的自动配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration(after = BztmasterRedisAutoConfiguration.class)
public class BztmasterApiSignatureAutoConfiguration {

    @Bean
    public ApiSignatureAspect signatureAspect(ApiSignatureRedisDAO signatureRedisDAO) {
        return new ApiSignatureAspect(signatureRedisDAO);
    }

    @Bean
    public ApiSignatureRedisDAO signatureRedisDAO(StringRedisTemplate stringRedisTemplate) {
        return new ApiSignatureRedisDAO(stringRedisTemplate);
    }

}
