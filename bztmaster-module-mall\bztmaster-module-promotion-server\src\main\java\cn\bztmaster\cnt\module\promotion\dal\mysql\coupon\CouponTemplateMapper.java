package cn.bztmaster.cnt.module.promotion.dal.mysql.coupon;

import cn.hutool.core.collection.CollUtil;
import cn.bztmaster.cnt.framework.common.enums.CommonStatusEnum;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.promotion.controller.admin.coupon.vo.template.CouponTemplatePageReqVO;
import cn.bztmaster.cnt.module.promotion.dal.dataobject.coupon.CouponTemplateDO;
import cn.bztmaster.cnt.module.promotion.enums.coupon.CouponTemplateValidityTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;
import java.util.function.Consumer;

/**
 * 优惠劵模板 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CouponTemplateMapper extends BaseMapperX<CouponTemplateDO> {

    default PageResult<CouponTemplateDO> selectPage(CouponTemplatePageReqVO reqVO) {
        // 构建可领取的查询条件
        Consumer<LambdaQueryWrapper<CouponTemplateDO>> canTakeConsumer = buildCanTakeQueryConsumer(reqVO.getCanTakeTypes());
        // 执行分页查询
        return selectPage(reqVO, new LambdaQueryWrapperX<CouponTemplateDO>()
                .likeIfPresent(CouponTemplateDO::getName, reqVO.getName())
                .eqIfPresent(CouponTemplateDO::getStatus, reqVO.getStatus())
                .eqIfPresent(CouponTemplateDO::getDiscountType, reqVO.getDiscountType())
                .betweenIfPresent(CouponTemplateDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(CouponTemplateDO::getProductScope, reqVO.getProductScope())
                .and(reqVO.getProductScopeValue() != null, w -> w.apply("FIND_IN_SET({0}, product_scope_values)",
                        reqVO.getProductScopeValue()))
                .and(canTakeConsumer != null, canTakeConsumer)
                .orderByDesc(CouponTemplateDO::getId));
    }

    default void updateTakeCount(Long id, Integer incrCount) {
        update(null, new LambdaUpdateWrapper<CouponTemplateDO>()
                .eq(CouponTemplateDO::getId, id)
                .setSql("take_count = take_count + " + incrCount));
    }

    default List<CouponTemplateDO> selectListByTakeType(Integer takeType) {
        return selectList(CouponTemplateDO::getTakeType, takeType, CouponTemplateDO::getStatus, CommonStatusEnum.ENABLE.getStatus());
    }

    default List<CouponTemplateDO> selectList(List<Integer> canTakeTypes, Integer productScope, Long productScopeValue, Integer count) {
        // 构建可领取的查询条件
        Consumer<LambdaQueryWrapper<CouponTemplateDO>> canTakeConsumer = buildCanTakeQueryConsumer(canTakeTypes);
        return selectList(new LambdaQueryWrapperX<CouponTemplateDO>()
                .eqIfPresent(CouponTemplateDO::getProductScope, productScope)
                .and(productScopeValue != null, w -> w.apply("FIND_IN_SET({0}, product_scope_values)",
                        productScopeValue))
                .and(canTakeConsumer != null, canTakeConsumer)
                .last(" LIMIT " + count)
                .orderByDesc(CouponTemplateDO::getId));
    }

    static Consumer<LambdaQueryWrapper<CouponTemplateDO>> buildCanTakeQueryConsumer(List<Integer> canTakeTypes) {
        Consumer<LambdaQueryWrapper<CouponTemplateDO>> canTakeConsumer = null;
        if (CollUtil.isNotEmpty(canTakeTypes)) {
            canTakeConsumer = w ->
                    w.eq(CouponTemplateDO::getStatus, CommonStatusEnum.ENABLE.getStatus()) // 1. 状态为可用的
                            .in(CouponTemplateDO::getTakeType, canTakeTypes) // 2. 领取方式一致
                            .and(ww -> ww.gt(CouponTemplateDO::getValidEndTime, LocalDateTime.now())  // 3.1 未过期
                                    .or().eq(CouponTemplateDO::getValidityType, CouponTemplateValidityTypeEnum.TERM.getType())) // 3.2 领取之后
                            .apply(" (take_count < total_count OR total_count = -1)"); // 4. 剩余数量大于 0，或者无限领取
        }
        return canTakeConsumer;
    }

}
