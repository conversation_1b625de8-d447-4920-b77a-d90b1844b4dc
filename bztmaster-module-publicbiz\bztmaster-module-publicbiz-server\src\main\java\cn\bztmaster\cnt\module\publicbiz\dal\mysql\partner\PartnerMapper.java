package cn.bztmaster.cnt.module.publicbiz.dal.mysql.partner;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo.PartnerPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.partner.PartnerDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;

/**
 * 合作伙伴 Mapper
 */
@Mapper
public interface PartnerMapper extends BaseMapperX<PartnerDO> {

        /**
         * 分页查询合作伙伴列表
         */
        default PageResult<PartnerDO> selectPage(PartnerPageReqVO reqVO) {
                LambdaQueryWrapperX<PartnerDO> queryWrapper = new LambdaQueryWrapperX<PartnerDO>()
                                .eqIfPresent(PartnerDO::getType, reqVO.getType())
                                .eqIfPresent(PartnerDO::getBiz, reqVO.getBiz())
                                .eqIfPresent(PartnerDO::getStatus, reqVO.getStatus())
                                .eqIfPresent(PartnerDO::getDeleted, false);

                // 关键字搜索：同时匹配机构名称和负责人姓名
                if (reqVO.getKeyword() != null && !reqVO.getKeyword().trim().isEmpty()) {
                        queryWrapper.and(wrapper -> wrapper
                                        .like(PartnerDO::getName, reqVO.getKeyword())
                                        .or()
                                        .like(PartnerDO::getOwnerName, reqVO.getKeyword()));
                }

                return selectPage(reqVO, queryWrapper.orderByDesc(PartnerDO::getId));
        }

        // 其它自定义方法可补充
}