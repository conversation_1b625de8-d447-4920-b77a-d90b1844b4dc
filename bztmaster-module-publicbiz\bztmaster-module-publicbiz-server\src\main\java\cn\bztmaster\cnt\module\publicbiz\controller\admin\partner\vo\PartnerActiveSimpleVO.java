package cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "有效状态合作伙伴下拉框数据 VO")
public class PartnerActiveSimpleVO {
    @Schema(description = "合作伙伴ID")
    private Long id;
    @Schema(description = "机构全称")
    private String name;
    @Schema(description = "机构简称")
    private String shortName;
    @Schema(description = "机构类型")
    private String type;
    @Schema(description = "业务模块")
    private String biz;
    @Schema(description = "合作状态")
    private String status;
    @Schema(description = "主要联系人")
    private String contactName;
    @Schema(description = "联系电话")
    private String contactPhone;
}