package cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * 数字资产课程 - 状态修改 Request VO
 * 对应前端页面：src/views/infra/ResourceCenter/DigitalAsset/DigitalAsset.vue
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "数字资产课程 - 状态修改 Request VO")
public class DigitalAssetCourseStatusReqVO {

    @NotBlank(message = "课程状态不能为空")
    @Schema(description = "课程状态", example = "已上架", requiredMode = Schema.RequiredMode.REQUIRED)
    private String status;
}
