package cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

/**
 * 用户标签关联表实体
 * 对应表：talent_user_tag
 */
@TableName("talent_user_tag")
@Data
public class TalentUserTagDO {
    @TableId
    private Long userTagId;
    private Long userId;
    private Long tagId;
    private Long tagTypeId;
    private Boolean deleted;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private Long tenantId;
}