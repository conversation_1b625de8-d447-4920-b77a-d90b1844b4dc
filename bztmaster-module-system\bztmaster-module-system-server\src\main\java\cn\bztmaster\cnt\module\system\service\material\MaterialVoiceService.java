package cn.bztmaster.cnt.module.system.service.material;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialVoiceDO;

import java.util.Collection;
import java.util.List;

public interface MaterialVoiceService {
    PageResult<VoiceRespVO> getVoicePage(VoicePageReqVO reqVO);
    Long createVoice(VoiceSaveReqVO reqVO);
    void updateVoice(VoiceSaveReqVO reqVO);
    void deleteVoice(Long id);
    VoiceRespVO getVoiceDetail(Long id);
    
    // API接口需要的方法
    MaterialVoiceDO getVoice(Long id);
    List<MaterialVoiceDO> getVoiceList(Collection<Long> ids);
    List<MaterialVoiceDO> getVoiceListByCategoryId(Long categoryId);
    List<MaterialVoiceDO> getVoiceListBySourceOrgId(Long sourceOrgId);
    void validateVoiceList(Collection<Long> ids);
    PageResult<VoiceRespVO> getVoiceRecyclePage(VoicePageReqVO reqVO, String updateTimeFrom, String updateTimeTo);
    void restoreVoiceFromRecycle(java.util.List<Long> idList);
    void deleteVoiceFromRecycle(java.util.List<Long> idList);
}
