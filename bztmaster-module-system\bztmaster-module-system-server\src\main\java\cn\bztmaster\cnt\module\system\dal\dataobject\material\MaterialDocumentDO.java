package cn.bztmaster.cnt.module.system.dal.dataobject.material;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * 文档素材 DO
 */
@TableName("mp_material_document")
@KeySequence("mp_material_document_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialDocumentDO extends BaseDO {

    /**
     * 文档ID
     */
    @TableId
    private Long id;
    /**
     * 文档名称
     */
    private String documentName;
    /**
     * 文档URL
     */
    private String documentUrl;
    /**
     * 分类ID
     */
    private Long categoryId;
    /**
     * 来源机构ID
     */
    private Long sourceOrgId;
    /**
     * 可视范围机构ID
     */
    private Long visibleOrgId;
    /**
     * 来源机构名称
     */
    private String sourceOrgName;
    /**
     * 可视范围机构名称
     */
    private String visibleOrgName;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 文档描述
     */
    private String description;
    /**
     * 状态
     */
    private Integer status;
} 