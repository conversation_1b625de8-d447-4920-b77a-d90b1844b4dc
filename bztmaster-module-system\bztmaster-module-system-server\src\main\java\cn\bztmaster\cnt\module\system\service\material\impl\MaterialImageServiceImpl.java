package cn.bztmaster.cnt.module.system.service.material.impl;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialImageDO;
import cn.bztmaster.cnt.module.system.dal.mysql.material.MaterialImageMapper;
import cn.bztmaster.cnt.module.system.service.material.MaterialImageService;
import cn.bztmaster.cnt.module.system.convert.material.MaterialImageConvert;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;

@Service
public class MaterialImageServiceImpl implements MaterialImageService {
    @Resource
    private MaterialImageMapper imageMapper;
    @Resource
    private MaterialImageConvert imageConvert;

    @Override
    public PageResult<ImageRespVO> getImagePage(ImagePageReqVO reqVO) {
        PageResult<MaterialImageDO> pageResult = imageMapper.selectPage(reqVO);
        return imageConvert.convertPage(pageResult);
    }

    @Override
    @Transactional
    public Long createImage(ImageSaveReqVO reqVO) {
        MaterialImageDO image = imageConvert.convert(reqVO);
        imageMapper.insert(image);
        return image.getId();
    }

    @Override
    @Transactional
    public void updateImage(ImageSaveReqVO reqVO) {
        MaterialImageDO image = imageConvert.convert(reqVO);
        imageMapper.updateById(image);
    }

    @Override
    @Transactional
    public void deleteImage(Long id) {
        imageMapper.logicDeleteById(id);
    }

    @Override
    public ImageRespVO getImageDetail(Long id) {
        MaterialImageDO image = imageMapper.selectById(id);
        if (image == null || Boolean.TRUE.equals(image.getDeleted())) return null;
        return imageConvert.convert(image);
    }

    @Override
    public PageResult<ImageRespVO> getImageRecyclePage(ImagePageReqVO reqVO, String updateTimeFrom, String updateTimeTo) {
        PageResult<MaterialImageDO> pageResult = imageMapper.selectRecyclePage(reqVO, updateTimeFrom, updateTimeTo);
        return imageConvert.convertPage(pageResult);
    }

    @Override
    @Transactional
    public void restoreImageFromRecycle(java.util.List<Long> idList) {
        if (idList == null || idList.isEmpty()) return;
        imageMapper.restoreFromRecycle(idList);
    }

    @Override
    @Transactional
    public void deleteImageFromRecycle(java.util.List<Long> idList) {
        if (idList == null || idList.isEmpty()) return;
        imageMapper.deleteFromRecycle(idList);
    }
} 