package cn.bztmaster.cnt.module.system.service.material.impl;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialDocumentDO;
import cn.bztmaster.cnt.module.system.dal.mysql.material.MaterialDocumentMapper;
import cn.bztmaster.cnt.module.system.service.material.MaterialDocumentService;
import cn.bztmaster.cnt.module.system.convert.material.MaterialDocumentConvert;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;

@Service
public class MaterialDocumentServiceImpl implements MaterialDocumentService {
    @Resource
    private MaterialDocumentMapper documentMapper;
    @Resource
    private MaterialDocumentConvert documentConvert;

    @Override
    public PageResult<DocumentRespVO> getDocumentPage(DocumentPageReqVO reqVO) {
        PageResult<MaterialDocumentDO> pageResult = documentMapper.selectPage(reqVO);
        return documentConvert.convertPage(pageResult);
    }

    @Override
    @Transactional
    public Long createDocument(DocumentSaveReqVO reqVO) {
        MaterialDocumentDO document = documentConvert.convert(reqVO);
        documentMapper.insert(document);
        return document.getId();
    }

    @Override
    @Transactional
    public void updateDocument(DocumentSaveReqVO reqVO) {
        MaterialDocumentDO document = documentConvert.convert(reqVO);
        documentMapper.updateById(document);
    }

    @Override
    @Transactional
    public void deleteDocument(Long id) {
        documentMapper.logicDeleteById(id);
    }

    @Override
    public DocumentRespVO getDocumentDetail(Long id) {
        MaterialDocumentDO document = documentMapper.selectById(id);
        if (document == null || Boolean.TRUE.equals(document.getDeleted())) return null;
        return documentConvert.convert(document);
    }

    @Override
    public PageResult<DocumentRespVO> getDocumentRecyclePage(DocumentPageReqVO reqVO, String updateTimeFrom, String updateTimeTo) {
        PageResult<MaterialDocumentDO> pageResult = documentMapper.selectRecyclePage(reqVO, updateTimeFrom, updateTimeTo);
        return documentConvert.convertPage(pageResult);
    }

    @Override
    @Transactional
    public void restoreDocumentFromRecycle(java.util.List<Long> idList) {
        if (idList == null || idList.isEmpty()) return;
        documentMapper.restoreFromRecycle(idList);
    }

    @Override
    @Transactional
    public void deleteDocumentFromRecycle(java.util.List<Long> idList) {
        if (idList == null || idList.isEmpty()) return;
        documentMapper.deleteFromRecycle(idList);
    }
} 