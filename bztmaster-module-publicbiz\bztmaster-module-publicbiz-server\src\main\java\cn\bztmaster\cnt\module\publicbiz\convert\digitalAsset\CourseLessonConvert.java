package cn.bztmaster.cnt.module.publicbiz.convert.digitalAsset;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset.CourseLessonDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
 * 课程课时 Convert
 * 
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface CourseLessonConvert {
    
    CourseLessonConvert INSTANCE = Mappers.getMapper(CourseLessonConvert.class);

    /**
     * VO 转 DO
     */
    CourseLessonDO convert(CourseLessonSaveReqVO bean);

    /**
     * DO 转 VO
     */
    CourseLessonRespVO convert(CourseLessonDO bean);

    /**
     * DO 列表转 VO 列表
     */
    List<CourseLessonRespVO> convertList(List<CourseLessonDO> list);
}
