package cn.bztmaster.cnt.module.talentpool.api.talent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 人才用户 Response DTO
 *
 * 用于返回人才库用户的详细信息，包括基本信息和子表信息。
 *
 * <AUTHOR>
 */
@Data
public class TalentUserRespDTO {

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "10001")
    private Long id;

    /**
     * 姓名
     */
    @Schema(description = "姓名", example = "张三")
    private String name;

    /**
     * 手机号
     */
    @Schema(description = "手机号", example = "138****1234")
    private String phone;

    /**
     * 身份证号
     */
    @Schema(description = "身份证号", example = "110101199001011234")
    private String identityId;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    /**
     * 性别
     */
    @Schema(description = "性别", example = "女")
    private String gender;

    /**
     * 出生日期
     */
    @Schema(description = "出生日期", example = "1990-03-07")
    private String birthDate;

    /**
     * 头像URL
     */
    @Schema(description = "头像URL", example = "https://xxx.com/avatar.jpg")
    private String avatarUrl;

    /**
     * 用户状态
     */
    @Schema(description = "用户状态", example = "正常")
    private String status;

    /**
     * 用户来源
     */
    @Schema(description = "用户来源", example = "高校实习小程序")
    private String source;

    /**
     * OneID
     */
    @Schema(description = "OneID", example = "xxxx-xxxx-xxxx-xxxx")
    private String oneid;

    /**
     * 用户标签
     */
    @Schema(description = "用户标签", example = "[\"认证:高级母婴护理\", \"已实名\"]")
    private List<String> tags;

    /**
     * 档案完整度（0-100）
     */
    @Schema(description = "档案完整度", example = "95")
    private Integer completeness;

    @Schema(description = "所属机构ID")
    private Long orgId;
    @Schema(description = "所属机构名称")
    private String orgName;
    @Schema(description = "人才来源")
    private String talentSource;
    @Schema(description = "是否为平台自营（0-否，1-是）")
    private Boolean isSelfSupport;

}
