package cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(description = "商机中心 - 商机新增/更新 Request VO")
public class BusinessSaveReqVO {
    private Long id;
    private Long tenantId;
    @NotBlank(message = "商机名称不能为空")
    private String name;
    @NotNull(message = "客户ID不能为空")
    private Long customerId;
    @NotBlank(message = "客户名称不能为空")
    private String customerName;
    private String businessType;
    private BigDecimal totalPrice;
    private Date expectedDealDate;
    private String businessStage;
    private String description;
    private Long ownerUserId;
    private String ownerUserName;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private Boolean deleted;
} 