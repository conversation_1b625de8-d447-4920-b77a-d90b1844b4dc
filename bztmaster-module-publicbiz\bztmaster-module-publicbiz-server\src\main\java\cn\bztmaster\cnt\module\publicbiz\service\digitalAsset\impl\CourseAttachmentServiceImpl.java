package cn.bztmaster.cnt.module.publicbiz.service.digitalAsset.impl;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.digitalAsset.CourseAttachmentConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset.CourseAttachmentDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.digitalAsset.CourseAttachmentMapper;
import cn.bztmaster.cnt.module.publicbiz.service.digitalAsset.CourseAttachmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.*;

/**
 * 课程附件 Service 实现类
 * 
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CourseAttachmentServiceImpl implements CourseAttachmentService {

    @Resource
    private CourseAttachmentMapper courseAttachmentMapper;

    @Override
    public List<CourseAttachmentRespVO> listCourseAttachment(Long courseId) {
        List<CourseAttachmentDO> list = courseAttachmentMapper.selectListByCourseId(courseId);
        return CourseAttachmentConvert.INSTANCE.convertList(list);
    }

    @Override
    public Long addCourseAttachment(CourseAttachmentSaveReqVO reqVO) {
        // 转换并插入
        CourseAttachmentDO attachment = CourseAttachmentConvert.INSTANCE.convert(reqVO);
        courseAttachmentMapper.insert(attachment);
        return attachment.getId();
    }

    @Override
    public void removeCourseAttachment(Long id) {
        // 校验存在
        validateAttachmentExists(id);
        // 删除
        courseAttachmentMapper.deleteById(id);
    }

    private void validateAttachmentExists(Long id) {
        if (courseAttachmentMapper.selectById(id) == null) {
            throw exception(COURSE_ATTACHMENT_NOT_EXISTS);
        }
    }
}
