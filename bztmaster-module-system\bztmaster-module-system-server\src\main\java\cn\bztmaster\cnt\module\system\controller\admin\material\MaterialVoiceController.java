package cn.bztmaster.cnt.module.system.controller.admin.material;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.service.material.MaterialVoiceService;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/system/material/voice")
@Tag(name = "素材库-音频管理")
@Slf4j
@Validated
public class MaterialVoiceController {
    @Resource
    private MaterialVoiceService voiceService;

    @GetMapping("/list")
    @Operation(summary = "音频列表")
    public CommonResult<PageResult<VoiceRespVO>> list(@Validated VoicePageReqVO reqVO) {
        return CommonResult.success(voiceService.getVoicePage(reqVO));
    }

    @PostMapping("/create")
    @Operation(summary = "新增音频")
    public CommonResult<Long> create(@RequestBody @Validated VoiceSaveReqVO reqVO) {
        return CommonResult.success(voiceService.createVoice(reqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "编辑音频")
    public CommonResult<Boolean> update(@RequestBody @Validated VoiceSaveReqVO reqVO) {
        voiceService.updateVoice(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除音频")
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        voiceService.deleteVoice(id);
        return CommonResult.success(true);
    }

    @GetMapping("/detail")
    @Operation(summary = "音频详情")
    public CommonResult<VoiceRespVO> detail(@RequestParam("id") Long id) {
        return CommonResult.success(voiceService.getVoiceDetail(id));
    }

    @GetMapping("/recycleList")
    @Operation(summary = "音频回收站列表")
    public CommonResult<PageResult<VoiceRespVO>> recycleList(@Validated VoicePageReqVO reqVO,
                                                            @RequestParam(value = "updateTimeFrom", required = false) String updateTimeFrom,
                                                            @RequestParam(value = "updateTimeTo", required = false) String updateTimeTo) {
        return CommonResult.success(voiceService.getVoiceRecyclePage(reqVO, updateTimeFrom, updateTimeTo));
    }

    @PostMapping("/recycleRestore")
    @Operation(summary = "音频回收站恢复")
    public CommonResult<Boolean> recycleRestore(@RequestBody List<Long> idList) {
        voiceService.restoreVoiceFromRecycle(idList);
        return CommonResult.success(true);
    }

    @PostMapping("/recycleDelete")
    @Operation(summary = "音频回收站永久删除")
    public CommonResult<Boolean> recycleDelete(@RequestBody List<Long> idList) {
        voiceService.deleteVoiceFromRecycle(idList);
        return CommonResult.success(true);
    }
}
