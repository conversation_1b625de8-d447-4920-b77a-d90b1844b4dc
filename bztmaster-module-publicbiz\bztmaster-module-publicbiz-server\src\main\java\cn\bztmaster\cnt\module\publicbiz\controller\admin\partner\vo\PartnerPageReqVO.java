package cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "资源中心 - 合作伙伴分页查询 Request VO")
public class PartnerPageReqVO extends PageParam {
    @Schema(description = "机构类型")
    private String type;

    @Schema(description = "业务模块")
    private String biz;

    @Schema(description = "合作状态")
    private String status;

    @Schema(description = "关键字（模糊匹配机构名称和负责人姓名）")
    private String keyword;

    @Schema(description = "租户编号")
    private Long tenantId;
    @Schema(description = "创建者")
    private String creator;
    @Schema(description = "更新者")
    private String updater;
    @Schema(description = "是否删除")
    private Boolean deleted;
}