-- 1.1 用户主表（talent_user）
INSERT INTO `talent_user` (`name`, `identity_id`, `birth_date`, `gender`, `phone`, `email`, `avatar_url`, `status`, `register_source`, `oneid`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
('张三', '110101199001010011', '1990-01-01', '男', '13800000001', 'z<PERSON><PERSON>@example.com', '', '正常', '小程序', 'ONEID-0001', 0, 1, NOW(), 1, NOW(), 1);
INSERT INTO `talent_user` (`name`, `identity_id`, `birth_date`, `gender`, `phone`, `email`, `avatar_url`, `status`, `register_source`, `oneid`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
('李四', '110101199202020022', '1992-02-02', '女', '13800000002', '<EMAIL>', '', '正常', '后台', 'ONEID-0002', 0, 1, NOW(), 1, NOW(), 1);
INSERT INTO `talent_user` (`name`, `identity_id`, `birth_date`, `gender`, `phone`, `email`, `avatar_url`, `status`, `register_source`, `oneid`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
('王五', '110101199303030033', '1993-03-03', '男', '13800000003', '<EMAIL>', '', '待合并', '小程序', 'ONEID-0003', 0, 1, NOW(), 1, NOW(), 1);
INSERT INTO `talent_user` (`name`, `identity_id`, `birth_date`, `gender`, `phone`, `email`, `avatar_url`, `status`, `register_source`, `oneid`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
('赵六', '110101199404040044', '1994-04-04', '女', '13800000004', '<EMAIL>', '', '已禁用', '后台', 'ONEID-0004', 0, 1, NOW(), 1, NOW(), 1);
INSERT INTO `talent_user` (`name`, `identity_id`, `birth_date`, `gender`, `phone`, `email`, `avatar_url`, `status`, `register_source`, `oneid`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
('钱七', '110101199505050055', '1995-05-05', '男', '13800000005', '<EMAIL>', '', '正常', '小程序', 'ONEID-0005', 0, 1, NOW(), 1, NOW(), 1);
INSERT INTO `talent_user` (`name`, `identity_id`, `birth_date`, `gender`, `phone`, `email`, `avatar_url`, `status`, `register_source`, `oneid`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
('孙八', '110101199606060066', '1996-06-06', '女', '13800000006', '<EMAIL>', '', '正常', '后台', 'ONEID-0006', 0, 1, NOW(), 1, NOW(), 1);
INSERT INTO `talent_user` (`name`, `identity_id`, `birth_date`, `gender`, `phone`, `email`, `avatar_url`, `status`, `register_source`, `oneid`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
('周九', '110101199707070077', '1997-07-07', '男', '13800000007', '<EMAIL>', '', '正常', '小程序', 'ONEID-0007', 0, 1, NOW(), 1, NOW(), 1);
INSERT INTO `talent_user` (`name`, `identity_id`, `birth_date`, `gender`, `phone`, `email`, `avatar_url`, `status`, `register_source`, `oneid`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
('吴十', '110101199808080088', '女', '1998-08-08', '13800000008', '<EMAIL>', '', '正常', '后台', 'ONEID-0008', 0, 1, NOW(), 1, NOW(), 1);
INSERT INTO `talent_user` (`name`, `identity_id`, `birth_date`, `gender`, `phone`, `email`, `avatar_url`, `status`, `register_source`, `oneid`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
('郑十一', '110101199909090099', '1999-09-09', '男', '13800000009', '<EMAIL>', '', '正常', '小程序', 'ONEID-0009', 0, 1, NOW(), 1, NOW(), 1);
INSERT INTO `talent_user` (`name`, `identity_id`, `birth_date`, `gender`, `phone`, `email`, `avatar_url`, `status`, `register_source`, `oneid`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
('王十二', '110101200001010010', '2000-01-01', '女', '13800000010', '<EMAIL>', '', '正常', '后台', 'ONEID-0010', 0, 1, NOW(), 1, NOW(), 1);
INSERT INTO `talent_user` (`name`, `identity_id`, `birth_date`, `gender`, `phone`, `email`, `avatar_url`, `status`, `register_source`, `oneid`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
('测试用户11', '110101200101010011', '2001-01-01', '男', '13800000011', '<EMAIL>', '', '正常', '高校实践小程序', 'ONEID-0011', 0, 1, NOW(), 1, NOW(), 1);
INSERT INTO `talent_user` (`name`, `identity_id`, `birth_date`, `gender`, `phone`, `email`, `avatar_url`, `status`, `register_source`, `oneid`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
('测试用户12', '110101200202020012', '2002-02-02', '女', '13800000012', '<EMAIL>', '', '正常', '家政服务员注册', 'ONEID-0012', 0, 1, NOW(), 1, NOW(), 1);
INSERT INTO `talent_user` (`name`, `identity_id`, `birth_date`, `gender`, `phone`, `email`, `avatar_url`, `status`, `register_source`, `oneid`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
('测试用户13', '110101200303030013', '2003-03-03', '男', '13800000013', '<EMAIL>', '', '正常', '技能培训报名', 'ONEID-0013', 0, 1, NOW(), 1, NOW(), 1);
INSERT INTO `talent_user` (`name`, `identity_id`, `birth_date`, `gender`, `phone`, `email`, `avatar_url`, `status`, `register_source`, `oneid`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
('测试用户14', '110101200404040014', '2004-04-04', '女', '13800000014', '<EMAIL>', '', '正常', '高校实践小程序', 'ONEID-0014', 0, 1, NOW(), 1, NOW(), 1);
INSERT INTO `talent_user` (`name`, `identity_id`, `birth_date`, `gender`, `phone`, `email`, `avatar_url`, `status`, `register_source`, `oneid`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
('测试用户15', '110101200505050015', '2005-05-05', '男', '13800000015', '<EMAIL>', '', '正常', '家政服务员注册', 'ONEID-0015', 0, 1, NOW(), 1, NOW(), 1);
INSERT INTO `talent_user` (`name`, `identity_id`, `birth_date`, `gender`, `phone`, `email`, `avatar_url`, `status`, `register_source`, `oneid`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
('测试用户16', '110101200606060016', '2006-06-06', '女', '13800000016', '<EMAIL>', '', '正常', '技能培训报名', 'ONEID-0016', 0, 1, NOW(), 1, NOW(), 1);
INSERT INTO `talent_user` (`name`, `identity_id`, `birth_date`, `gender`, `phone`, `email`, `avatar_url`, `status`, `register_source`, `oneid`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
('测试用户17', '110101200707070017', '2007-07-07', '男', '13800000017', '<EMAIL>', '', '正常', '高校实践小程序', 'ONEID-0017', 0, 1, NOW(), 1, NOW(), 1);
INSERT INTO `talent_user` (`name`, `identity_id`, `birth_date`, `gender`, `phone`, `email`, `avatar_url`, `status`, `register_source`, `oneid`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
('测试用户18', '110101200808080018', '女', '2008-08-08', '13800000018', '<EMAIL>', '', '正常', '家政服务员注册', 'ONEID-0018', 0, 1, NOW(), 1, NOW(), 1);
INSERT INTO `talent_user` (`name`, `identity_id`, `birth_date`, `gender`, `phone`, `email`, `avatar_url`, `status`, `register_source`, `oneid`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
('测试用户19', '110101200909090019', '男', '2009-09-09', '13800000019', '<EMAIL>', '', '正常', '技能培训报名', 'ONEID-0019', 0, 1, NOW(), 1, NOW(), 1);
INSERT INTO `talent_user` (`name`, `identity_id`, `birth_date`, `gender`, `phone`, `email`, `avatar_url`, `status`, `register_source`, `oneid`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
('测试用户20', '110101201010100020', '2010-10-10', '女', '13800000020', '<EMAIL>', '', '正常', '高校实践小程序', 'ONEID-0020', 0, 1, NOW(), 1, NOW(), 1);

-- 1.2 教育背景表（talent_education）
INSERT INTO `talent_education` (`user_id`, `institution`, `college_address`, `degree_type`, `major`, `start_date`, `end_date`, `academic_ranking`, `is_internship`, `internship_type`, `internship_duration`, `deleted`, `creator`, `create_time`, `updater`, `update_time`) VALUES
(1, '清华大学', '北京市', '本科', '计算机科学', '2008-09-01', '2012-07-01', '前10%', 1, '生产实习', 60, 0, 1, NOW(), 1, NOW());
INSERT INTO `talent_education` (`user_id`, `institution`, `college_address`, `degree_type`, `major`, `start_date`, `end_date`, `academic_ranking`, `is_internship`, `internship_type`, `internship_duration`, `deleted`, `creator`, `create_time`, `updater`, `update_time`) VALUES
(2, '北京大学', '北京市', '硕士', '金融学', '2012-09-01', '2015-07-01', '前20%', 0, NULL, NULL, 0, 1, NOW(), 1, NOW());
INSERT INTO `talent_education` (`user_id`, `institution`, `college_address`, `degree_type`, `major`, `start_date`, `end_date`, `academic_ranking`, `is_internship`, `internship_type`, `internship_duration`, `deleted`, `creator`, `create_time`, `updater`, `update_time`) VALUES
(3, '复旦大学', '上海市', '博士', '物理学', '2010-09-01', '2016-07-01', '前5%', 1, '毕业实习', 90, 0, 1, NOW(), 1, NOW());
INSERT INTO `talent_education` (`user_id`, `institution`, `college_address`, `degree_type`, `major`, `start_date`, `end_date`, `academic_ranking`, `is_internship`, `internship_type`, `internship_duration`, `deleted`, `creator`, `create_time`, `updater`, `update_time`) VALUES
(4, '浙江大学', '杭州市', '本科', '机械工程', '2011-09-01', '2015-07-01', '前30%', 0, NULL, NULL, 0, 1, NOW(), 1, NOW());
INSERT INTO `talent_education` (`user_id`, `institution`, `college_address`, `degree_type`, `major`, `start_date`, `end_date`, `academic_ranking`, `is_internship`, `internship_type`, `internship_duration`, `deleted`, `creator`, `create_time`, `updater`, `update_time`) VALUES
(5, '上海交通大学', '上海市', '硕士', '管理学', '2013-09-01', '2016-07-01', '前15%', 1, '认知实习', 30, 0, 1, NOW(), 1, NOW());
INSERT INTO `talent_education` (`user_id`, `institution`, `college_address`, `degree_type`, `major`, `start_date`, `end_date`, `academic_ranking`, `is_internship`, `internship_type`, `internship_duration`, `deleted`, `creator`, `create_time`, `updater`, `update_time`) VALUES
(6, '南京大学', '南京市', '本科', '化学', '2012-09-01', '2016-07-01', '前25%', 0, NULL, NULL, 0, 1, NOW(), 1, NOW());
INSERT INTO `talent_education` (`user_id`, `institution`, `college_address`, `degree_type`, `major`, `start_date`, `end_date`, `academic_ranking`, `is_internship`, `internship_type`, `internship_duration`, `deleted`, `creator`, `create_time`, `updater`, `update_time`) VALUES
(7, '中国人民大学', '北京市', '硕士', '法学', '2014-09-01', '2017-07-01', '前10%', 1, '其他', 45, 0, 1, NOW(), 1, NOW());
INSERT INTO `talent_education` (`user_id`, `institution`, `college_address`, `degree_type`, `major`, `start_date`, `end_date`, `academic_ranking`, `is_internship`, `internship_type`, `internship_duration`, `deleted`, `creator`, `create_time`, `updater`, `update_time`) VALUES
(8, '武汉大学', '武汉市', '本科', '生物学', '2013-09-01', '2017-07-01', '前40%', 0, NULL, NULL, 0, 1, NOW(), 1, NOW());
INSERT INTO `talent_education` (`user_id`, `institution`, `college_address`, `degree_type`, `major`, `start_date`, `end_date`, `academic_ranking`, `is_internship`, `internship_type`, `internship_duration`, `deleted`, `creator`, `create_time`, `updater`, `update_time`) VALUES
(9, '中山大学', '广州市', '博士', '数学', '2011-09-01', '2017-07-01', '前5%', 1, '生产实习', 60, 0, 1, NOW(), 1, NOW());
INSERT INTO `talent_education` (`user_id`, `institution`, `college_address`, `degree_type`, `major`, `start_date`, `end_date`, `academic_ranking`, `is_internship`, `internship_type`, `internship_duration`, `deleted`, `creator`, `create_time`, `updater`, `update_time`) VALUES
(10, '哈尔滨工业大学', '哈尔滨市', '本科', '土木工程', '2014-09-01', '2018-07-01', '前50%', 0, NULL, NULL, 0, 1, NOW(), 1, NOW());

-- 1.3 校园实践表（talent_campus_practice）
INSERT INTO `talent_campus_practice` (`user_id`, `practice_name`, `organizer`, `start_date`, `end_date`, `practice_report`, `deleted`, `creator`, `create_time`, `updater`, `update_time`) VALUES
(1, '创新创业大赛', '清华大学', '2011-05-01', '2011-06-01', '参与创新创业大赛，获得二等奖', 0, '1', NOW(), '1', NOW()),
(2, '暑期社会实践', '北京大学', '2013-07-01', '2013-08-01', '深入农村调研社会问题', 0, '1', NOW(), '1', NOW()),
(3, '志愿服务活动', '复旦大学', '2014-03-01', '2014-04-01', '参与志愿服务，服务时长40小时', 0, '1', NOW(), '1', NOW()),
(4, '科技创新营', '浙江大学', '2012-10-01', '2012-11-01', '完成科技创新项目', 0, '1', NOW(), '1', NOW()),
(5, '学生会干部培训', '上海交通大学', '2015-03-01', '2015-04-01', '担任学生会干部，组织多项活动', 0, '1', NOW(), '1', NOW()),
(6, '文艺汇演', '南京大学', '2014-06-01', '2014-07-01', '参与文艺汇演，表演舞蹈', 0, '1', NOW(), '1', NOW()),
(7, '社会调研', '中国人民大学', '2016-08-01', '2016-09-01', '参与社会调研，撰写调研报告', 0, '1', NOW(), '1', NOW()),
(8, '创新实验班', '武汉大学', '2015-09-01', '2015-10-01', '参加创新实验班，完成课题', 0, '1', NOW(), '1', NOW()),
(9, '学术论坛', '中山大学', '2017-04-01', '2017-05-01', '参加学术论坛并发表论文', 0, '1', NOW(), '1', NOW()),
(10, '公益活动', '哈尔滨工业大学', '2016-11-01', '2016-12-01', '参与公益活动，服务社区', 0, '1', NOW(), '1', NOW());

-- 1.4 实习经历表（talent_internship）
INSERT INTO `talent_internship` (`user_id`, `company`, `position`, `start_date`, `end_date`, `responsibilities`, `deleted`, `creator`, `create_time`, `updater`, `update_time`) VALUES
(1, '百度', '软件工程师实习生', '2012-07-01', '2012-09-01', '参与后端开发', 0, '1', NOW(), '1', NOW()),
(2, '阿里巴巴', '数据分析实习生', '2015-07-01', '2015-09-01', '数据清洗与分析', 0, '1', NOW(), '1', NOW()),
(3, '腾讯', '产品助理实习生', '2016-07-01', '2016-09-01', '产品需求调研', 0, '1', NOW(), '1', NOW()),
(4, '字节跳动', '运营实习生', '2015-07-01', '2015-09-01', '内容运营', 0, '1', NOW(), '1', NOW()),
(5, '美团', '测试工程师实习生', '2016-07-01', '2016-09-01', '软件测试', 0, '1', NOW(), '1', NOW()),
(6, '京东', '市场实习生', '2017-07-01', '2017-09-01', '市场推广', 0, '1', NOW(), '1', NOW()),
(7, '携程', '客服实习生', '2018-07-01', '2018-09-01', '客户服务', 0, '1', NOW(), '1', NOW()),
(8, '网易', '前端开发实习生', '2017-07-01', '2017-09-01', '前端页面开发', 0, '1', NOW(), '1', NOW()),
(9, '小米', '硬件工程师实习生', '2018-07-01', '2018-09-01', '硬件测试', 0, '1', NOW(), '1', NOW()),
(10, '华为', '算法实习生', '2019-07-01', '2019-09-01', '算法优化', 0, '1', NOW(), '1', NOW());

-- 1.5 项目经历表（talent_project）
INSERT INTO `talent_project` (`user_id`, `name`, `description`, `start_date`, `end_date`, `deleted`, `creator`, `create_time`, `updater`, `update_time`) VALUES
(1, '智能推荐系统', '开发基于大数据的推荐系统', '2011-09-01', '2012-06-01', 0, '1', NOW(), '1', NOW()),
(2, '金融风控平台', '参与金融风控平台设计', '2014-09-01', '2015-06-01', 0, '1', NOW(), '1', NOW()),
(3, '物联网网关', '设计物联网数据采集网关', '2015-09-01', '2016-06-01', 0, '1', NOW(), '1', NOW()),
(4, '机械臂控制系统', '开发机械臂运动控制算法', '2013-09-01', '2014-06-01', 0, '1', NOW(), '1', NOW()),
(5, '企业管理系统', '参与企业管理系统开发', '2015-09-01', '2016-06-01', 0, '1', NOW(), '1', NOW()),
(6, '化学实验自动化', '开发化学实验自动化平台', '2014-09-01', '2015-06-01', 0, '1', NOW(), '1', NOW()),
(7, '法律文书自动生成', '实现法律文书自动生成工具', '2016-09-01', '2017-06-01', 0, '1', NOW(), '1', NOW()),
(8, '生物信息分析', '参与生物信息分析平台开发', '2015-09-01', '2016-06-01', 0, '1', NOW(), '1', NOW()),
(9, '数学建模平台', '开发数学建模与仿真平台', '2016-09-01', '2017-06-01', 0, '1', NOW(), '1', NOW()),
(10, '土木工程BIM', '参与BIM建模与分析', '2017-09-01', '2018-06-01', 0, '1', NOW(), '1', NOW());

-- 1.6 培训记录表（talent_training）
INSERT INTO `talent_training` (`user_id`, `provider`, `course`, `complete_date`, `deleted`, `creator`, `create_time`, `updater`, `update_time`) VALUES
(1, '华为培训中心', '云计算基础', '2013-08-01', 0, '1', NOW(), '1', NOW()),
(2, '阿里云大学', '大数据分析', '2016-08-01', 0, '1', NOW(), '1', NOW()),
(3, '腾讯课堂', '产品经理入门', '2017-08-01', 0, '1', NOW(), '1', NOW()),
(4, '百度学院', '人工智能基础', '2015-08-01', 0, '1', NOW(), '1', NOW()),
(5, '网易云课堂', '前端开发', '2017-08-01', 0, '1', NOW(), '1', NOW()),
(6, '京东大学', '市场营销', '2018-08-01', 0, '1', NOW(), '1', NOW()),
(7, '携程学院', '客户服务', '2019-08-01', 0, '1', NOW(), '1', NOW()),
(8, '美团大学', '软件测试', '2018-08-01', 0, '1', NOW(), '1', NOW()),
(9, '小米学院', '硬件开发', '2019-08-01', 0, '1', NOW(), '1', NOW()),
(10, '字节跳动学院', '算法优化', '2020-08-01', 0, '1', NOW(), '1', NOW());

-- 1.7 技能清单表（talent_skill）
INSERT INTO `talent_skill` (`user_id`, `name`, `level`, `deleted`, `creator`, `create_time`, `updater`, `update_time`) VALUES
(1, 'Java', '精通', 0, '1', NOW(), '1', NOW()),
(2, 'Python', '熟悉', 0, '1', NOW(), '1', NOW()),
(3, 'SQL', '了解', 0, '1', NOW(), '1', NOW()),
(4, 'C++', '精通', 0, '1', NOW(), '1', NOW()),
(5, 'Excel', '熟悉', 0, '1', NOW(), '1', NOW()),
(6, 'PPT', '了解', 0, '1', NOW(), '1', NOW()),
(7, '法律文书写作', '精通', 0, '1', NOW(), '1', NOW()),
(8, '生物信息分析', '熟悉', 0, '1', NOW(), '1', NOW()),
(9, '数学建模', '精通', 0, '1', NOW(), '1', NOW()),
(10, 'BIM建模', '了解', 0, '1', NOW(), '1', NOW());

-- 1.8 认证与资质表（talent_certificate）
INSERT INTO `talent_certificate` (`user_id`, `name`, `issuer`, `issue_date`, `source`, `status`, `deleted`, `creator`, `create_time`, `updater`, `update_time`) VALUES
(1, '计算机二级证书', '教育部', '2012-06-01', 'PLATFORM', 'VERIFIED', 0, '1', NOW(), '1', NOW()),
(2, 'CFA一级', 'CFA协会', '2015-06-01', 'SELF', 'PENDING_VERIFICATION', 0, '1', NOW(), '1', NOW()),
(3, '英语六级', '教育部', '2016-06-01', 'PLATFORM', 'VERIFIED', 0, '1', NOW(), '1', NOW()),
(4, '律师资格证', '司法部', '2017-06-01', 'AGENCY', 'VERIFIED', 0, '1', NOW(), '1', NOW()),
(5, '教师资格证', '教育部', '2018-06-01', 'PLATFORM', 'REJECTED', 0, '1', NOW(), '1', NOW()),
(6, 'PMP', 'PMI', '2019-06-01', 'SELF', 'VERIFIED', 0, '1', NOW(), '1', NOW()),
(7, '会计证', '财政部', '2017-06-01', 'PLATFORM', 'VERIFIED', 0, '1', NOW(), '1', NOW()),
(8, '生物分析师证', '生物协会', '2018-06-01', 'AGENCY', 'PENDING_VERIFICATION', 0, '1', NOW(), '1', NOW()),
(9, '数学建模竞赛证书', '数学会', '2019-06-01', 'SELF', 'VERIFIED', 0, '1', NOW(), '1', NOW()),
(10, 'BIM工程师证书', '住建部', '2020-06-01', 'PLATFORM', 'VERIFIED', 0, '1', NOW(), '1', NOW());

-- 1.9 求职记录表（talent_job_application）
INSERT INTO `talent_job_application` (`user_id`, `company`, `position`, `apply_date`, `status`, `deleted`, `creator`, `create_time`, `updater`, `update_time`) VALUES
(1, '字节跳动', '后端开发', '2022-03-01', '已投递', 0, '1', NOW(), '1', NOW()),
(2, '腾讯', '数据分析师', '2022-03-02', '面试中', 0, '1', NOW(), '1', NOW()),
(3, '阿里巴巴', '产品经理', '2022-03-03', '已录用', 0, '1', NOW(), '1', NOW()),
(4, '百度', '测试工程师', '2022-03-04', '未通过', 0, '1', NOW(), '1', NOW()),
(5, '美团', '市场专员', '2022-03-05', '已投递', 0, '1', NOW(), '1', NOW()),
(6, '京东', '运营专员', '2022-03-06', '面试中', 0, '1', NOW(), '1', NOW()),
(7, '携程', '客服', '2022-03-07', '已录用', 0, '1', NOW(), '1', NOW()),
(8, '网易', '前端开发', '2022-03-08', '未通过', 0, '1', NOW(), '1', NOW()),
(9, '小米', '硬件工程师', '2022-03-09', '已投递', 0, '1', NOW(), '1', NOW()),
(10, '华为', '算法工程师', '2022-03-10', '面试中', 0, '1', NOW(), '1', NOW());

-- 1.10 工作履历表（talent_employment）
INSERT INTO `talent_employment` (`user_id`, `company`, `position`, `start_date`, `end_date`, `salary`, `deleted`, `creator`, `create_time`, `updater`, `update_time`) VALUES
(1, '百度', '后端开发', '2012-09-01', '2015-08-01', 15000.00, 0, '1', NOW(), '1', NOW()),
(2, '阿里巴巴', '数据分析师', '2015-09-01', '2018-08-01', 18000.00, 0, '1', NOW(), '1', NOW()),
(3, '腾讯', '产品经理', '2016-09-01', '2019-08-01', 20000.00, 0, '1', NOW(), '1', NOW()),
(4, '字节跳动', '运营', '2015-09-01', '2018-08-01', 16000.00, 0, '1', NOW(), '1', NOW()),
(5, '美团', '测试工程师', '2016-09-01', '2019-08-01', 14000.00, 0, '1', NOW(), '1', NOW()),
(6, '京东', '市场专员', '2017-09-01', '2020-08-01', 13000.00, 0, '1', NOW(), '1', NOW()),
(7, '携程', '客服', '2018-09-01', '2021-08-01', 12000.00, 0, '1', NOW(), '1', NOW()),
(8, '网易', '前端开发', '2017-09-01', '2020-08-01', 17000.00, 0, '1', NOW(), '1', NOW()),
(9, '小米', '硬件工程师', '2018-09-01', '2021-08-01', 19000.00, 0, '1', NOW(), '1', NOW()),
(10, '华为', '算法工程师', '2019-09-01', '2022-08-01', 21000.00, 0, '1', NOW(), '1', NOW());

-- 1.11 人才标签表（talent_user_tag）
INSERT INTO `talent_user_tag` (`user_id`, `tag_id`, `tag_type_id`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
(1, 1, 1, 0, '1', NOW(), '1', NOW(), 1),
(2, 2, 1, 0, '1', NOW(), '1', NOW(), 1),
(3, 3, 2, 0, '1', NOW(), '1', NOW(), 1),
(4, 4, 2, 0, '1', NOW(), '1', NOW(), 1),
(5, 5, 3, 0, '1', NOW(), '1', NOW(), 1),
(6, 6, 3, 0, '1', NOW(), '1', NOW(), 1),
(7, 7, 4, 0, '1', NOW(), '1', NOW(), 1),
(8, 8, 4, 0, '1', NOW(), '1', NOW(), 1),
(9, 9, 5, 0, '1', NOW(), '1', NOW(), 1),
(10, 10, 5, 0, '1', NOW(), '1', NOW(), 1);

-- 1.11.1 新增：user_id=27~33的每个用户各2条标签（tag_id仅用1~10循环分配）
INSERT INTO `talent_user_tag` (`user_id`, `tag_id`, `tag_type_id`, `deleted`, `creator`, `create_time`, `updater`, `update_time`, `tenant_id`) VALUES
(27, 1, 1, 0, '1', NOW(), '1', NOW(), 1),
(27, 2, 2, 0, '1', NOW(), '1', NOW(), 1),
(28, 3, 3, 0, '1', NOW(), '1', NOW(), 1),
(28, 4, 4, 0, '1', NOW(), '1', NOW(), 1),
(29, 5, 5, 0, '1', NOW(), '1', NOW(), 1),
(29, 6, 1, 0, '1', NOW(), '1', NOW(), 1),
(30, 7, 2, 0, '1', NOW(), '1', NOW(), 1),
(30, 8, 3, 0, '1', NOW(), '1', NOW(), 1),
(31, 9, 4, 0, '1', NOW(), '1', NOW(), 1),
(31, 10, 5, 0, '1', NOW(), '1', NOW(), 1),
(32, 1, 1, 0, '1', NOW(), '1', NOW(), 1),
(32, 2, 2, 0, '1', NOW(), '1', NOW(), 1),
(33, 3, 3, 0, '1', NOW(), '1', NOW(), 1),
(33, 4, 4, 0, '1', NOW(), '1', NOW(), 1);

-- 1.12 标签类型表（talent_tag_type）
INSERT INTO `talent_tag_type` (`type_code`, `type_name`, `description`, `deleted`, `creator`, `create_time`, `updater`, `