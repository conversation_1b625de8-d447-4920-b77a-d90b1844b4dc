<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.digitalAsset.DigitalAssetCourseMapper">
    
    <resultMap id="BaseResultMap" type="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset.DigitalAssetCourseDO">
        <id property="id" column="id" />
        <result property="name" column="name" />
        <result property="teachType" column="teach_type" />
        <result property="coverUrl" column="cover_url" />
        <result property="category" column="category" />
        <result property="status" column="status" />
        <result property="teacherId" column="teacher_id" />
        <result property="teacherName" column="teacher_name" />
        <result property="businessModule" column="business_module" />
        <result property="merchant" column="merchant" />
        <result property="merchantName" column="merchant_name" />
        <result property="description" column="description" />
        <result property="location" column="location" />
        <result property="schedule" column="schedule" />
        <result property="totalSeats" column="total_seats" />
        <result property="enrolledCount" column="enrolled_count" />
        <result property="totalDuration" column="total_duration" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="creator" column="creator" />
        <result property="updater" column="updater" />
        <result property="deleted" column="deleted" />
        <result property="tenantId" column="tenant_id" />
    </resultMap>

    <!-- 分页查询课程列表 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT id, name, teach_type, cover_url, category, status, teacher_id, teacher_name, 
               business_module, merchant, merchant_name, description, location, schedule, 
               total_seats, enrolled_count, total_duration, create_time, update_time, 
               creator, updater, deleted, tenant_id
        FROM publicbiz_digital_asset_course
        WHERE deleted = 0
        <if test="reqVO.keyword != null and reqVO.keyword != ''">
            AND name LIKE CONCAT('%', #{reqVO.keyword}, '%')
        </if>
        <if test="reqVO.category != null and reqVO.category != ''">
            AND category = #{reqVO.category}
        </if>
        <if test="reqVO.status != null and reqVO.status != ''">
            AND status = #{reqVO.status}
        </if>
        <if test="reqVO.teachType != null and reqVO.teachType != ''">
            AND teach_type = #{reqVO.teachType}
        </if>
        <if test="reqVO.businessModule != null and reqVO.businessModule != ''">
            AND business_module = #{reqVO.businessModule}
        </if>
        <if test="reqVO.merchant != null">
            AND merchant = #{reqVO.merchant}
        </if>
        ORDER BY id DESC
    </select>

    <!-- 获取课程统计概览 -->
    <select id="selectStatistics" resultType="cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo.CourseStatisticsRespVO">
        SELECT 
            COUNT(*) as totalCount,
            COUNT(CASE WHEN teach_type = '线上授课' THEN 1 END) as onlineCount,
            COUNT(CASE WHEN teach_type = '线下授课' THEN 1 END) as offlineCount,
            COUNT(CASE WHEN status = '已上架' THEN 1 END) as publishedCount
        FROM publicbiz_digital_asset_course
        WHERE deleted = 0
    </select>

    <!-- 根据课程名称查询（用于重复性校验） -->
    <select id="selectByName" resultMap="BaseResultMap">
        SELECT id, name, teach_type, cover_url, category, status, teacher_id, teacher_name, 
               business_module, merchant, merchant_name, description, location, schedule, 
               total_seats, enrolled_count, total_duration, create_time, update_time, 
               creator, updater, deleted, tenant_id
        FROM publicbiz_digital_asset_course
        WHERE deleted = 0 AND name = #{name}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
        LIMIT 1
    </select>

    <!-- 根据业务板块统计课程数量 -->
    <select id="countByBusinessModule" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM publicbiz_digital_asset_course
        WHERE deleted = 0 AND business_module = #{businessModule}
    </select>

    <!-- 根据讲师ID查询课程列表 -->
    <select id="selectByTeacherId" resultMap="BaseResultMap">
        SELECT id, name, teach_type, cover_url, category, status, teacher_id, teacher_name, 
               business_module, merchant, merchant_name, description, location, schedule, 
               total_seats, enrolled_count, total_duration, create_time, update_time, 
               creator, updater, deleted, tenant_id
        FROM publicbiz_digital_asset_course
        WHERE deleted = 0 AND teacher_id = #{teacherId}
        ORDER BY create_time DESC
    </select>

</mapper>
