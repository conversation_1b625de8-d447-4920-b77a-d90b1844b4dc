package cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 数字资产课程 - 新增/更新 Request VO
 * 对应前端页面：src/views/infra/ResourceCenter/DigitalAsset/components/AddDigitalAsset.vue
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "数字资产课程 - 新增/更新 Request VO")
public class DigitalAssetCourseSaveReqVO {

    @Schema(description = "课程ID", example = "1")
    private Long id;

    @NotBlank(message = "课程名称不能为空")
    @Schema(description = "课程名称", example = "金牌月嫂职业技能培训", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @NotBlank(message = "授课方式不能为空")
    @Schema(description = "授课方式", example = "线上授课", requiredMode = Schema.RequiredMode.REQUIRED)
    private String teachType;

    @Schema(description = "课程封面图片URL", example = "https://example.com/cover.jpg")
    private String coverUrl;

    @NotBlank(message = "课程分类不能为空")
    @Schema(description = "课程分类", example = "家政技能", requiredMode = Schema.RequiredMode.REQUIRED)
    private String category;

    @NotBlank(message = "课程状态不能为空")
    @Schema(description = "课程状态", example = "已上架", requiredMode = Schema.RequiredMode.REQUIRED)
    private String status;

    @Schema(description = "关联讲师ID", example = "1001")
    private Long teacherId;

    @Schema(description = "关联讲师名称", example = "王老师")
    private String teacherName;

    @Schema(description = "所属业务板块", example = "家政服务")
    private String businessModule;

    @Schema(description = "收款商户ID", example = "1001")
    private Long merchant;

    @Schema(description = "收款商户名称", example = "汇成家政服务")
    private String merchantName;

    @Schema(description = "课程详情介绍")
    private String description;

    @Schema(description = "上课地点（线下授课专用）", example = "北京市朝阳区培训中心")
    private String location;

    @Schema(description = "排期安排（线下授课专用）", example = "每周一、三、五 9:00-17:00")
    private String schedule;

    @Schema(description = "总名额（线下授课专用）", example = "30")
    private Integer totalSeats;

    @Schema(description = "已报名人数", example = "25")
    private Integer enrolledCount;

    @Schema(description = "课程总时长（小时，线上授课专用）", example = "10.5")
    private BigDecimal totalDuration;
}
