package cn.bztmaster.cnt.module.publicbiz.controller.admin.leads.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 线索信息 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 线索信息 Response VO")
@Data
public class LeadsRespVO {

    @Schema(description = "主键，自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "线索ID，系统生成的唯一标识", requiredMode = Schema.RequiredMode.REQUIRED, example = "LEAD20250721001")
    private String leadId;

    @Schema(description = "客户姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String customerName;

    @Schema(description = "联系电话，11位手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "***********")
    private String customerPhone;

    @Schema(description = "线索来源", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer leadSource;

    @Schema(description = "线索来源描述", example = "官网注册")
    private String leadSourceDesc;

    @Schema(description = "业务模块", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer businessModule;

    @Schema(description = "业务模块描述", example = "高校业务")
    private String businessModuleDesc;

    @Schema(description = "线索状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer leadStatus;

    @Schema(description = "线索状态描述", example = "未处理")
    private String leadStatusDesc;

    @Schema(description = "创建方式", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer createMethod;

    @Schema(description = "创建方式描述", example = "手动创建")
    private String createMethodDesc;

    @Schema(description = "当前跟进人ID", example = "1024")
    private String currentOwner;

    @Schema(description = "当前跟进人姓名", example = "李四")
    private String currentOwnerName;

    @Schema(description = "创建人姓名", example = "张三")
    private String creatorName;

    @Schema(description = "备注信息", example = "客户对产品很感兴趣")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "创建时间（格式化）", example = "2025-07-22")
    private String createTimeFormatted;

    @Schema(description = "创建人", example = "admin")
    private String creator;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime updateTime;

    @Schema(description = "更新人", example = "admin")
    private String updater;
}