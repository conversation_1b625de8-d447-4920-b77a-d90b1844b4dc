package cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "商机中心 - 商机日志分页 Request VO")
public class BusinessLogPageReqVO extends PageParam {
    private Long id;
    private Long tenantId;
    private Long businessId;
    private String action;
    private String content;
    private Long actionUserId;
    private String actionUserName;
    private String creator;
    private String updater;
    private Boolean deleted;
} 