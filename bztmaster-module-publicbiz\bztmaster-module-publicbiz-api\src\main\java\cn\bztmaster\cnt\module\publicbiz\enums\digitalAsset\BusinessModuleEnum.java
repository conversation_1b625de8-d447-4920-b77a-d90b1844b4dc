package cn.bztmaster.cnt.module.publicbiz.enums.digitalAsset;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数字资产课程业务板块枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BusinessModuleEnum {

    DOMESTIC_SERVICE("家政服务", "家政服务业务"),
    UNIVERSITY_PRACTICE("高校实践", "高校实践业务"),
    TRAINING_MANAGEMENT("培训管理", "培训管理业务"),
    EMPLOYMENT_SERVICE("就业服务", "就业服务业务"),
    PART_TIME_WORK("兼职零工", "兼职零工业务");

    /**
     * 业务板块值
     */
    private final String module;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据业务板块值获取枚举
     *
     * @param module 业务板块值
     * @return 枚举
     */
    public static BusinessModuleEnum getByModule(String module) {
        if (module == null) {
            return null;
        }
        for (BusinessModuleEnum value : BusinessModuleEnum.values()) {
            if (value.getModule().equals(module)) {
                return value;
            }
        }
        return null;
    }
}
