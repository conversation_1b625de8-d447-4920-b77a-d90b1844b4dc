package cn.bztmaster.cnt.module.system.convert.material;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialNewsDO;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper(componentModel = "spring")
public interface MaterialNewsConvert {
    MaterialNewsConvert INSTANCE = Mappers.getMapper(MaterialNewsConvert.class);

    MaterialNewsDO convert(NewsSaveReqVO bean);
    NewsRespVO convert(MaterialNewsDO bean);
    List<NewsRespVO> convertList(List<MaterialNewsDO> list);
    PageResult<NewsRespVO> convertPage(PageResult<MaterialNewsDO> page);
} 