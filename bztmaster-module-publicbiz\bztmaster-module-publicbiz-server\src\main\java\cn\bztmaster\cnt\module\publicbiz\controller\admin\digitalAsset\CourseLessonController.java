package cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.digitalAsset.CourseLessonService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 课程课时管理 Controller
 * 对应前端页面：src/views/infra/ResourceCenter/DigitalAsset/components/ManagementCourseForOnline.vue
 * 
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 课程课时管理")
@RestController
@RequestMapping("/publicbiz/digital-asset/course/lesson")
@Validated
public class CourseLessonController {

    @Resource
    private CourseLessonService courseLessonService;

    @PostMapping("/add")
    @Operation(summary = "新增课程课时")
    public CommonResult<Long> createCourseLesson(@Valid @RequestBody CourseLessonSaveReqVO reqVO) {
        Long lessonId = courseLessonService.createCourseLesson(reqVO);
        return CommonResult.success(lessonId);
    }

    @PutMapping("/update")
    @Operation(summary = "更新课程课时")
    public CommonResult<Boolean> updateCourseLesson(@Valid @RequestBody CourseLessonSaveReqVO reqVO) {
        courseLessonService.updateCourseLesson(reqVO);
        return CommonResult.success(true);
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除课程课时")
    @Parameter(name = "id", description = "课时ID", required = true)
    public CommonResult<Boolean> deleteCourseLesson(@PathVariable("id") Long id) {
        courseLessonService.deleteCourseLesson(id);
        return CommonResult.success(true);
    }
}
