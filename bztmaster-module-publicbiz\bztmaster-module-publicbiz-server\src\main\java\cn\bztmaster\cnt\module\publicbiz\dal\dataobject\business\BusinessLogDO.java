package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.business;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@TableName("publicbiz_business_log")
@Schema(description = "商机操作日志表 DO")
public class BusinessLogDO {
    @TableId
    private Long id;
    private Long tenantId;
    private Long businessId;
    private String action;
    private String content;
    private Date actionTime;
    private Long actionUserId;
    private String actionUserName;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private Boolean deleted;
} 