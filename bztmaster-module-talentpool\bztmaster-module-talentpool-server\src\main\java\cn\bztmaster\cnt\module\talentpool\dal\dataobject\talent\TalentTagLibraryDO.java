package cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

/**
 * 标签库表实体
 * 对应表：talent_tag_library
 */
@TableName("talent_tag_library")
@Data
public class TalentTagLibraryDO {
    @TableId
    private Long tagId;
    private Long tagTypeId;
    private String tagCode;
    private String tagName;
    private String description;
    private Boolean deleted;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private Long tenantId;
}