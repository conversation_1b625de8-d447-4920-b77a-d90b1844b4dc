package cn.bztmaster.cnt.module.publicbiz.api.digitalAsset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 数字资产课程 Response DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "数字资产课程 Response DTO")
public class DigitalAssetCourseRespDTO {

    @Schema(description = "课程ID", example = "1")
    private Long id;

    @Schema(description = "课程名称", example = "金牌月嫂职业技能培训")
    private String name;

    @Schema(description = "授课方式", example = "线上授课")
    private String teachType;

    @Schema(description = "课程封面图片URL", example = "https://example.com/cover.jpg")
    private String coverUrl;

    @Schema(description = "课程分类", example = "家政技能")
    private String category;

    @Schema(description = "课程状态", example = "已上架")
    private String status;

    @Schema(description = "关联讲师ID", example = "1001")
    private Long teacherId;

    @Schema(description = "关联讲师名称", example = "王老师")
    private String teacherName;

    @Schema(description = "所属业务板块", example = "家政服务")
    private String businessModule;

    @Schema(description = "收款商户ID", example = "1001")
    private Long merchant;

    @Schema(description = "收款商户名称", example = "汇成家政服务")
    private String merchantName;

    @Schema(description = "课程详情介绍")
    private String description;

    @Schema(description = "上课地点（线下授课专用）", example = "北京市朝阳区培训中心")
    private String location;

    @Schema(description = "排期安排（线下授课专用）", example = "每周一、三、五 9:00-17:00")
    private String schedule;

    @Schema(description = "总名额（线下授课专用）", example = "30")
    private Integer totalSeats;

    @Schema(description = "已报名人数", example = "25")
    private Integer enrolledCount;

    @Schema(description = "课程总时长（小时，线上授课专用）", example = "10.5")
    private BigDecimal totalDuration;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "租户编号", example = "1")
    private Long tenantId;
}
