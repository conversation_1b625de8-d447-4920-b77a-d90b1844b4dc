package cn.bztmaster.cnt.module.system.controller.admin.material.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@Schema(description = "管理后台 - 视频素材 Response VO")
public class VideoRespVO {
    @Schema(description = "视频ID", example = "1")
    private Long id;

    @Schema(description = "视频名称", example = "示例视频")
    private String name;

    @Schema(description = "视频URL", example = "https://example.com/video.mp4")
    private String url;

    @Schema(description = "缩略图URL", example = "https://example.com/thumb.jpg")
    private String thumbUrl;

    @Schema(description = "来源机构ID", example = "1")
    private Long sourceOrgId;

    @Schema(description = "来源机构名称", example = "内部素材库")
    private String sourceOrgName;

    @Schema(description = "分类ID", example = "1")
    private Long categoryId;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "最后更新日期")
    private Date updateTime;

    @Schema(description = "可视范围机构ID")
    private Long visibleOrgId;

    @Schema(description = "可视范围机构名称")
    private String visibleOrgName;
} 