package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

/**
 * 数字资产课程表 DO
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("publicbiz_digital_asset_course")
@Schema(description = "数字资产课程表 DO")
public class DigitalAssetCourseDO extends BaseDO {

    /**
     * 课程ID
     */
    @TableId
    @Schema(description = "课程ID", example = "1")
    private Long id;

    /**
     * 课程名称
     */
    @Schema(description = "课程名称", example = "金牌月嫂职业技能培训")
    private String name;

    /**
     * 授课方式：线上授课、线下授课
     */
    @Schema(description = "授课方式", example = "线上授课")
    private String teachType;

    /**
     * 课程封面图片URL
     */
    @Schema(description = "课程封面图片URL", example = "https://example.com/cover.jpg")
    private String coverUrl;

    /**
     * 课程分类：家政技能、职业素养、高校实践、企业管理
     */
    @Schema(description = "课程分类", example = "家政技能")
    private String category;

    /**
     * 课程状态：待发布、已上架、已下架
     */
    @Schema(description = "课程状态", example = "已上架")
    private String status;

    /**
     * 关联讲师ID
     */
    @Schema(description = "关联讲师ID", example = "1001")
    private Long teacherId;

    /**
     * 关联讲师名称
     */
    @Schema(description = "关联讲师名称", example = "王老师")
    private String teacherName;

    /**
     * 所属业务板块：家政服务、高校实践、培训管理、就业服务、兼职零工
     */
    @Schema(description = "所属业务板块", example = "家政服务")
    private String businessModule;

    /**
     * 收款商户ID
     */
    @Schema(description = "收款商户ID", example = "1001")
    private Long merchant;

    /**
     * 收款商户名称
     */
    @Schema(description = "收款商户名称", example = "汇成家政服务")
    private String merchantName;

    /**
     * 课程详情介绍
     */
    @Schema(description = "课程详情介绍")
    private String description;

    /**
     * 上课地点（线下授课专用）
     */
    @Schema(description = "上课地点", example = "北京市朝阳区培训中心")
    private String location;

    /**
     * 排期安排（线下授课专用）
     */
    @Schema(description = "排期安排", example = "每周一、三、五 9:00-17:00")
    private String schedule;

    /**
     * 总名额（线下授课专用）
     */
    @Schema(description = "总名额", example = "30")
    private Integer totalSeats;

    /**
     * 已报名人数
     */
    @Schema(description = "已报名人数", example = "25")
    private Integer enrolledCount;

    /**
     * 课程总时长（小时，线上授课专用）
     */
    @Schema(description = "课程总时长", example = "10.5")
    private BigDecimal totalDuration;
}
