package cn.bztmaster.cnt.module.publicbiz.controller.admin.leads.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

/**
 * 线索跟进记录保存 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 线索跟进记录保存 Request VO")
@Data
public class LeadFollowUpLogSaveReqVO {

    @Schema(description = "主键，更新时必传", example = "1024")
    private Long id;

    @Schema(description = "关联的线索ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "LEAD20250721001")
    @NotEmpty(message = "线索ID不能为空")
    @Size(max = 32, message = "线索ID长度不能超过32")
    private String leadId;

    @Schema(description = "跟进内容详情", requiredMode = Schema.RequiredMode.REQUIRED, example = "客户表示对产品很感兴趣，计划下周再次沟通")
    @NotEmpty(message = "跟进内容不能为空")
    private String followUpContent;
}