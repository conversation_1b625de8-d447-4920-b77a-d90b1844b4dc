-- =====================================================
-- 素材库数据库表结构
-- 创建时间：2025年
-- 说明：微信公众平台素材管理系统数据库表结构
-- =====================================================

-- 1. 素材分类表
CREATE TABLE `mp_material_category` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  
  `name` VARCHAR(100) NOT NULL COMMENT '分类名称',
  `parent_id` BIGINT DEFAULT 0 COMMENT '父分类ID，0表示顶级分类',
  `sort` INT DEFAULT 0 COMMENT '排序，数字越小越靠前',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态，0-禁用，1-启用',
  `description` VARCHAR(500) COMMENT '分类描述',
  `icon` VARCHAR(255) COMMENT '分类图标',
  `level` TINYINT DEFAULT 1 COMMENT '分类层级，1-一级，2-二级，以此类推',
  `path` VARCHAR(500) COMMENT '分类路径，如：1,2,3',
  `visible_org_id` BIGINT COMMENT '可视范围机构ID',
  `visible_org_name` VARCHAR(200) COMMENT '可视范围机构名称',

  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  PRIMARY KEY (`id`),
  INDEX `idx_parent_id` (`parent_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_sort` (`sort`)
) COMMENT='素材分类表';

-- 2. 图片素材表
CREATE TABLE `mp_material_image` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  
  `name` VARCHAR(200) NOT NULL COMMENT '图片名称',
  `category_id` BIGINT COMMENT '分类ID，关联mp_material_category.id',
  `media_id` VARCHAR(100) COMMENT '微信媒体ID',
  `url` VARCHAR(500) NOT NULL COMMENT '图片URL地址',
  `local_url` VARCHAR(500) COMMENT '本地存储路径',
  `file_name` VARCHAR(255) COMMENT '原始文件名',
  `file_size` BIGINT COMMENT '文件大小（字节）',
  `file_type` VARCHAR(50) COMMENT '文件类型，如：image/jpeg',
  `width` INT COMMENT '图片宽度（像素）',
  `height` INT COMMENT '图片高度（像素）',
  `format` VARCHAR(20) COMMENT '图片格式，如：jpg、png、gif',
  `source_type` TINYINT DEFAULT 1 COMMENT '素材来源类型，1-本地上传，2-微信同步，3-外部链接',
  `source_org_id` BIGINT COMMENT '来源机构ID',
  `source_org_name` VARCHAR(200) COMMENT '来源机构名称',
  `account_id` BIGINT COMMENT '公众号账号ID',
  `status` TINYINT DEFAULT 1 COMMENT '状态，0-禁用，1-启用',
  `tags` VARCHAR(500) COMMENT '标签，多个标签用逗号分隔',
  `description` VARCHAR(1000) COMMENT '图片描述',
  `is_permanent` TINYINT(1) DEFAULT 0 COMMENT '是否永久素材，0-临时，1-永久',
  `expire_time` DATETIME COMMENT '过期时间（临时素材）',
  `visible_org_id` BIGINT COMMENT '可视范围机构ID',
  `visible_org_name` VARCHAR(200) COMMENT '可视范围机构名称',

  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  PRIMARY KEY (`id`),
  INDEX `idx_category_id` (`category_id`),
  INDEX `idx_media_id` (`media_id`),
  INDEX `idx_account_id` (`account_id`),
  INDEX `idx_source_org_id` (`source_org_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_create_time` (`create_time`)
) COMMENT='图片素材表';

-- 3. 视频素材表
CREATE TABLE `mp_material_video` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  
  `name` VARCHAR(200) NOT NULL COMMENT '视频名称',
  `category_id` BIGINT COMMENT '分类ID，关联mp_material_category.id',
  `media_id` VARCHAR(100) COMMENT '微信媒体ID',
  `url` VARCHAR(500) NOT NULL COMMENT '视频URL地址',
  `local_url` VARCHAR(500) COMMENT '本地存储路径',
  `file_name` VARCHAR(255) COMMENT '原始文件名',
  `file_size` BIGINT COMMENT '文件大小（字节）',
  `file_type` VARCHAR(50) COMMENT '文件类型，如：video/mp4',
  `duration` INT COMMENT '视频时长（秒）',
  `width` INT COMMENT '视频宽度（像素）',
  `height` INT COMMENT '视频高度（像素）',
  `format` VARCHAR(20) COMMENT '视频格式，如：mp4、avi、mov',
  `title` VARCHAR(200) COMMENT '视频标题',
  `introduction` VARCHAR(1000) COMMENT '视频介绍',
  `thumb_media_id` VARCHAR(100) COMMENT '缩略图媒体ID',
  `thumb_url` VARCHAR(500) COMMENT '缩略图URL',
  `source_type` TINYINT DEFAULT 1 COMMENT '素材来源类型，1-本地上传，2-微信同步，3-外部链接',
  `source_org_id` BIGINT COMMENT '来源机构ID',
  `source_org_name` VARCHAR(200) COMMENT '来源机构名称',
  `account_id` BIGINT COMMENT '公众号账号ID',
  `status` TINYINT DEFAULT 1 COMMENT '状态，0-禁用，1-启用',
  `tags` VARCHAR(500) COMMENT '标签，多个标签用逗号分隔',
  `is_permanent` TINYINT(1) DEFAULT 0 COMMENT '是否永久素材，0-临时，1-永久',
  `expire_time` DATETIME COMMENT '过期时间（临时素材）',
  `visible_org_id` BIGINT COMMENT '可视范围机构ID',
  `visible_org_name` VARCHAR(200) COMMENT '可视范围机构名称',

  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  PRIMARY KEY (`id`),
  INDEX `idx_category_id` (`category_id`),
  INDEX `idx_media_id` (`media_id`),
  INDEX `idx_account_id` (`account_id`),
  INDEX `idx_source_org_id` (`source_org_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_create_time` (`create_time`)
) COMMENT='视频素材表';

-- 4. 语音素材表
CREATE TABLE `mp_material_voice` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  
  `name` VARCHAR(200) NOT NULL COMMENT '语音名称',
  `category_id` BIGINT COMMENT '分类ID，关联mp_material_category.id',
  `media_id` VARCHAR(100) COMMENT '微信媒体ID',
  `url` VARCHAR(500) NOT NULL COMMENT '语音URL地址',
  `local_url` VARCHAR(500) COMMENT '本地存储路径',
  `file_name` VARCHAR(255) COMMENT '原始文件名',
  `file_size` BIGINT COMMENT '文件大小（字节）',
  `file_type` VARCHAR(50) COMMENT '文件类型，如：audio/mp3',
  `duration` INT COMMENT '语音时长（秒）',
  `format` VARCHAR(20) COMMENT '语音格式，如：mp3、wma、wav、amr',
  `source_type` TINYINT DEFAULT 1 COMMENT '素材来源类型，1-本地上传，2-微信同步，3-外部链接',
  `source_org_id` BIGINT COMMENT '来源机构ID',
  `source_org_name` VARCHAR(200) COMMENT '来源机构名称',
  `account_id` BIGINT COMMENT '公众号账号ID',
  `status` TINYINT DEFAULT 1 COMMENT '状态，0-禁用，1-启用',
  `tags` VARCHAR(500) COMMENT '标签，多个标签用逗号分隔',
  `description` VARCHAR(1000) COMMENT '语音描述',
  `is_permanent` TINYINT(1) DEFAULT 0 COMMENT '是否永久素材，0-临时，1-永久',
  `expire_time` DATETIME COMMENT '过期时间（临时素材）',
  `visible_org_id` BIGINT COMMENT '可视范围机构ID',
  `visible_org_name` VARCHAR(200) COMMENT '可视范围机构名称',

  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  PRIMARY KEY (`id`),
  INDEX `idx_category_id` (`category_id`),
  INDEX `idx_media_id` (`media_id`),
  INDEX `idx_account_id` (`account_id`),
  INDEX `idx_source_org_id` (`source_org_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_create_time` (`create_time`)
) COMMENT='语音素材表';

-- 5. 文章素材表
CREATE TABLE `mp_material_article` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  
  `title` VARCHAR(200) NOT NULL COMMENT '文章标题',
  `category_id` BIGINT COMMENT '分类ID，关联mp_material_category.id',
  `content` LONGTEXT COMMENT '文章内容（HTML格式）',
  `content_text` LONGTEXT COMMENT '文章纯文本内容',
  `summary` VARCHAR(500) COMMENT '文章摘要',
  `author` VARCHAR(100) COMMENT '作者',
  `cover_url` VARCHAR(500) COMMENT '封面图片URL',
  `cover_media_id` VARCHAR(100) COMMENT '封面图片媒体ID',
  `source_type` TINYINT DEFAULT 1 COMMENT '素材来源类型，1-本地上传，2-微信同步，3-外部链接',
  `source_org_id` BIGINT COMMENT '来源机构ID',
  `source_org_name` VARCHAR(200) COMMENT '来源机构名称',
  `source_url` VARCHAR(500) COMMENT '原文链接',
  `account_id` BIGINT COMMENT '公众号账号ID',
  `status` TINYINT DEFAULT 1 COMMENT '状态，0-草稿，1-已发布，2-已下线',
  `publish_time` DATETIME COMMENT '发布时间',
  `tags` VARCHAR(500) COMMENT '标签，多个标签用逗号分隔',
  `keywords` VARCHAR(500) COMMENT '关键词，多个关键词用逗号分隔',
  `read_count` BIGINT DEFAULT 0 COMMENT '阅读数',
  `like_count` BIGINT DEFAULT 0 COMMENT '点赞数',
  `comment_count` BIGINT DEFAULT 0 COMMENT '评论数',
  `share_count` BIGINT DEFAULT 0 COMMENT '分享数',
  `is_top` TINYINT(1) DEFAULT 0 COMMENT '是否置顶，0-否，1-是',
  `is_recommend` TINYINT(1) DEFAULT 0 COMMENT '是否推荐，0-否，1-是',
  `sort` INT DEFAULT 0 COMMENT '排序，数字越小越靠前',
  `visible_org_id` BIGINT COMMENT '可视范围机构ID',
  `visible_org_name` VARCHAR(200) COMMENT '可视范围机构名称',

  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  PRIMARY KEY (`id`),
  INDEX `idx_category_id` (`category_id`),
  INDEX `idx_account_id` (`account_id`),
  INDEX `idx_source_org_id` (`source_org_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_publish_time` (`publish_time`),
  INDEX `idx_create_time` (`create_time`),
  FULLTEXT KEY `idx_title_content` (`title`, `content_text`)
) COMMENT='文章素材表';
-- 6. 文档素材表
CREATE TABLE `mp_material_document` (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  document_name VARCHAR(128) NOT NULL COMMENT '文档名称',
  document_url VARCHAR(512) NOT NULL COMMENT '文档内容（文档URL）',
  category_id BIGINT COMMENT '分类ID',
  source_org_id BIGINT COMMENT '来源机构ID',
  source_org_name VARCHAR(128) COMMENT '来源机构名称',
  sort INT DEFAULT 0 COMMENT '排序，数字越小越靠前',
  visible_org_id BIGINT COMMENT '可视范围机构ID',
  visible_org_name VARCHAR(200) COMMENT '可视范围机构名称',
  tenant_id BIGINT COMMENT '租户ID',
  creator VARCHAR(64) COMMENT '创建人',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater VARCHAR(64) COMMENT '更新人',
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  description VARCHAR(255) DEFAULT NULL COMMENT '文档描述',
  status TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态（1-正常，0-禁用）',
  INDEX idx_category_id (category_id),
  INDEX idx_source_org_id (source_org_id)
) COMMENT='文档素材表';
-- 7. 图文素材表
CREATE TABLE `mp_material_news` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `name` VARCHAR(200) NOT NULL COMMENT '图文名称',
  `category_id` BIGINT COMMENT '分类ID，关联mp_material_category.id',
  `media_id` VARCHAR(100) COMMENT '微信媒体ID',
  `content` LONGTEXT COMMENT '图文内容，文案内容',
  `thumb_url` VARCHAR(500) COMMENT '缩略图URL',
  `article_count` TINYINT DEFAULT 1 COMMENT '文章数量',
  `source_type` TINYINT DEFAULT 1 COMMENT '素材来源类型，1-本地上传，2-微信同步，3-外部链接',
  `source_org_id` BIGINT COMMENT '来源机构ID',
  `source_org_name` VARCHAR(200) COMMENT '来源机构名称',
  `account_id` BIGINT COMMENT '公众号账号ID',
  `status` TINYINT DEFAULT 1 COMMENT '状态，0-草稿，1-已发布，2-已下线',
  `publish_time` DATETIME COMMENT '发布时间',
  `tags` VARCHAR(500) COMMENT '标签，多个标签用逗号分隔',
  `description` VARCHAR(1000) COMMENT '图文描述',
  `is_permanent` TINYINT(1) DEFAULT 0 COMMENT '是否永久素材，0-临时，1-永久',
  `expire_time` DATETIME COMMENT '过期时间（临时素材）',
  `news_type` TINYINT DEFAULT 1 COMMENT '图文类型，1-已发布，2-草稿',
  `visible_org_id` BIGINT COMMENT '可视范围机构ID',
  `visible_org_name` VARCHAR(200) COMMENT '可视范围机构名称',

  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  PRIMARY KEY (`id`),
  INDEX `idx_category_id` (`category_id`),
  INDEX `idx_media_id` (`media_id`),
  INDEX `idx_account_id` (`account_id`),
  INDEX `idx_source_org_id` (`source_org_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_news_type` (`news_type`),
  INDEX `idx_create_time` (`create_time`)
) COMMENT='图文素材表';

-- 8. 图文文章详情表
CREATE TABLE `mp_material_news_item` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  
  `news_id` BIGINT NOT NULL COMMENT '图文ID，关联mp_material_news.id',
  `title` VARCHAR(200) NOT NULL COMMENT '文章标题',
  `digest` VARCHAR(500) COMMENT '文章摘要',
  `content` LONGTEXT COMMENT '文章内容（HTML格式）',
  `content_source_url` VARCHAR(500) COMMENT '原文链接',
  `thumb_media_id` VARCHAR(100) COMMENT '缩略图媒体ID',
  `thumb_url` VARCHAR(500) COMMENT '缩略图URL',
  `author` VARCHAR(100) COMMENT '作者',
  `show_cover_pic` TINYINT(1) DEFAULT 0 COMMENT '是否显示封面，0-否，1-是',
  `sort` INT DEFAULT 0 COMMENT '排序，数字越小越靠前',
  `url` VARCHAR(500) COMMENT '文章链接',
  `need_open_comment` TINYINT(1) DEFAULT 0 COMMENT '是否打开评论，0-否，1-是',
  `only_fans_can_comment` TINYINT(1) DEFAULT 0 COMMENT '是否粉丝才可评论，0-否，1-是',

  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  PRIMARY KEY (`id`),
  INDEX `idx_news_id` (`news_id`),
  INDEX `idx_sort` (`sort`)
) COMMENT='图文文章详情表';

-- 9. 素材标签表
CREATE TABLE `mp_material_tag` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  
  `name` VARCHAR(50) NOT NULL COMMENT '标签名称',
  `color` VARCHAR(20) COMMENT '标签颜色',
  `description` VARCHAR(200) COMMENT '标签描述',
  `sort` INT DEFAULT 0 COMMENT '排序，数字越小越靠前',
  `status` TINYINT DEFAULT 1 COMMENT '状态，0-禁用，1-启用',
  `use_count` INT DEFAULT 0 COMMENT '使用次数',

  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  INDEX `idx_status` (`status`),
  INDEX `idx_sort` (`sort`)
) COMMENT='素材标签表';

-- 10. 素材标签关联表
CREATE TABLE `mp_material_tag_relation` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  
  `tag_id` BIGINT NOT NULL COMMENT '标签ID，关联mp_material_tag.id',
  `material_id` BIGINT NOT NULL COMMENT '素材ID',
  `material_type` VARCHAR(20) NOT NULL COMMENT '素材类型，image-图片，video-视频，voice-语音，article-文章，news-图文',

  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tag_material` (`tag_id`, `material_id`, `material_type`),
  INDEX `idx_material` (`material_id`, `material_type`)
) COMMENT='素材标签关联表';

-- 11. 来源机构表
CREATE TABLE `mp_source_organization` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  
  `name` VARCHAR(200) NOT NULL COMMENT '机构名称',
  `code` VARCHAR(50) COMMENT '机构编码',
  `type` TINYINT DEFAULT 1 COMMENT '机构类型，1-内部机构，2-外部机构，3-合作伙伴',
  `logo` VARCHAR(500) COMMENT '机构Logo',
  `description` VARCHAR(1000) COMMENT '机构描述',
  `contact_person` VARCHAR(100) COMMENT '联系人',
  `contact_phone` VARCHAR(50) COMMENT '联系电话',
  `contact_email` VARCHAR(100) COMMENT '联系邮箱',
  `website` VARCHAR(500) COMMENT '官方网站',
  `address` VARCHAR(500) COMMENT '机构地址',
  `status` TINYINT DEFAULT 1 COMMENT '状态，0-禁用，1-启用',
  `sort` INT DEFAULT 0 COMMENT '排序，数字越小越靠前',

  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  INDEX `idx_type` (`type`),
  INDEX `idx_status` (`status`),
  INDEX `idx_sort` (`sort`)
) COMMENT='来源机构表';

-- 12. 素材使用记录表
CREATE TABLE `mp_material_usage_log` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  
  `material_id` BIGINT NOT NULL COMMENT '素材ID',
  `material_type` VARCHAR(20) NOT NULL COMMENT '素材类型，image-图片，video-视频，voice-语音，article-文章，news-图文',
  `usage_type` VARCHAR(50) NOT NULL COMMENT '使用类型，如：reply-回复消息，menu-自定义菜单，draft-草稿箱等',
  `usage_context` VARCHAR(200) COMMENT '使用场景描述',
  `account_id` BIGINT COMMENT '公众号账号ID',
  `user_id` BIGINT COMMENT '操作用户ID',
  `ip_address` VARCHAR(50) COMMENT '操作IP地址',
  `user_agent` VARCHAR(500) COMMENT '用户代理信息',

  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',


  PRIMARY KEY (`id`),
  INDEX `idx_material` (`material_id`, `material_type`),
  INDEX `idx_usage_type` (`usage_type`),
  INDEX `idx_account_id` (`account_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_create_time` (`create_time`)
) COMMENT='素材使用记录表';

-- =====================================================
-- 初始化数据
-- =====================================================

-- 插入默认分类
INSERT INTO `mp_material_category` (`name`, `parent_id`, `sort`, `status`, `description`, `level`, `path`) VALUES
('默认分类', 0, 1, 1, '系统默认分类', 1, '1'),
('图片素材', 0, 2, 1, '图片类型素材', 1, '2'),
('视频素材', 0, 3, 1, '视频类型素材', 1, '3'),
('文章素材', 0, 4, 1, '文章类型素材', 1, '4'),
('图文素材', 0, 5, 1, '图文类型素材', 1, '5');

-- 插入默认来源机构
INSERT INTO `mp_source_organization` (`name`, `code`, `type`, `description`, `status`, `sort`) VALUES
('内部素材库', 'INTERNAL', 1, '系统内部素材库', 1, 1),
('外部素材库', 'EXTERNAL', 2, '外部素材来源', 1, 2),
('合作伙伴', 'PARTNER', 3, '合作伙伴素材', 1, 3);

-- 插入默认标签
INSERT INTO `mp_material_tag` (`name`, `color`, `description`, `sort`, `status`) VALUES
('热门', '#ff4d4f', '热门素材标签', 1, 1),
('推荐', '#52c41a', '推荐素材标签', 2, 1),
('新品', '#1890ff', '新品素材标签', 3, 1),
('精选', '#faad14', '精选素材标签', 4, 1);

-- =====================================================
-- 索引优化建议
-- =====================================================

-- 建议在以下字段上添加复合索引以提升查询性能：
-- 1. 素材表按分类和状态查询：`idx_category_status`
-- 2. 素材表按账号和类型查询：`idx_account_type`
-- 3. 素材表按来源机构查询：`idx_source_org`
-- 4. 素材表按创建时间范围查询：`idx_create_time_range`

-- 示例索引创建语句：
-- ALTER TABLE `mp_material_image` ADD INDEX `idx_category_status` (`category_id`, `status`);
-- ALTER TABLE `mp_material_video` ADD INDEX `idx_account_type` (`account_id`, `status`);
-- ALTER TABLE `mp_material_article` ADD INDEX `idx_source_org` (`source_org_id`, `status`);

-- =====================================================
-- 表结构说明
-- =====================================================

/*
1. 素材分类表 (mp_material_category)
   - 支持多级分类结构
   - 通过parent_id实现树形结构
   - path字段记录完整路径，便于查询

2. 图片素材表 (mp_material_image)
   - 存储图片的基本信息和文件信息
   - 支持微信媒体ID和本地存储
   - 记录图片尺寸、格式等技术参数

3. 视频素材表 (mp_material_video)
   - 存储视频的基本信息和文件信息
   - 支持缩略图和视频介绍
   - 记录视频时长、分辨率等技术参数

4. 语音素材表 (mp_material_voice)
   - 存储语音的基本信息和文件信息
   - 记录语音时长、格式等技术参数

5. 文章素材表 (mp_material_article)
   - 存储文章的基本信息和内容
   - 支持HTML格式的内容存储
   - 记录阅读数、点赞数等统计数据

6. 图文素材表 (mp_material_news)
   - 存储图文的基本信息
   - content字段使用JSON格式存储多个文章
   - 支持草稿和已发布两种状态

7. 图文文章详情表 (mp_material_news_item)
   - 存储图文中的具体文章内容
   - 与图文表是一对多关系

8. 素材标签表 (mp_material_tag)
   - 存储标签的基本信息
   - 支持标签颜色和描述

9. 素材标签关联表 (mp_material_tag_relation)
   - 实现素材和标签的多对多关系
   - 支持不同类型的素材

10. 来源机构表 (mp_source_organization)
    - 存储素材来源机构信息
    - 支持内部、外部、合作伙伴等类型

11. 素材使用记录表 (mp_material_usage_log)
    - 记录素材的使用情况
    - 便于统计分析和审计

所有表都包含标准的公共字段：
- id: 主键
- tenant_id: 租户ID（支持多租户）
- creator/updater: 创建人/更新人
- create_time/update_time: 创建时间/更新时间
- deleted: 软删除标记
*/ 