package cn.bztmaster.cnt.module.infra.dal.mysql.demo;

import java.util.*;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.module.infra.dal.dataobject.demo.InfraStudentTeacherDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 学生班主任 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfraStudentTeacherMapper extends BaseMapperX<InfraStudentTeacherDO> {

    default InfraStudentTeacherDO selectByStudentId(Long studentId) {
        return selectOne(InfraStudentTeacherDO::getStudentId, studentId);
    }

    default int deleteByStudentId(Long studentId) {
        return delete(InfraStudentTeacherDO::getStudentId, studentId);
    }

}