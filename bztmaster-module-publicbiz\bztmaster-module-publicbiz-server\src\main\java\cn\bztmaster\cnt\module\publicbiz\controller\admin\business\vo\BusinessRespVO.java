package cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(description = "商机中心 - 商机 Response VO")
public class BusinessRespVO {
    private Long id;
    private Long tenantId;
    private String name;
    private Long customerId;
    private String customerName;
    private String businessType;
    private BigDecimal totalPrice;
    private Date expectedDealDate;
    private String businessStage;
    private String description;
    private Long ownerUserId;
    private String ownerUserName;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private Boolean deleted;
} 