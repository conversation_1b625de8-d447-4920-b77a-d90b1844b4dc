操作日志，记录「谁」在「什么时间」对「什么对象」做了「什么事情」。


1. 操作日志组件
    操作日志的记录，由 yudao-spring-boot-starter-security技术组件的 operatelog 包提供，基于我开源的 https://github.com/mouzt/mzt-biz-log
    实现，只需要添加 @LogRecord 注解，即可实现操作日志的记录。
    例如：
    【新增】2021-09-16 10:00 订单创建，订单号：NO.11089999，其中涉及变量订单号 “NO.11089999”
    【修改】2021-09-16 10:00 用户小明修改了订单的配送地址：从 “金灿灿小区” 修改到 “银盏盏小区”
    
2. 操作日志的存储，由 yudao-module-system 的 OperateLog模块实现，记录到数据库的 system_operate_log表
表结构如下：
CREATE TABLE `system_operate_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `trace_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '链路追踪编号',
  `user_id` bigint(20) NOT NULL COMMENT '用户编号',
  `user_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '用户类型',
  `type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作模块类型',
  `sub_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作名',
  `biz_id` bigint(20) NOT NULL COMMENT '操作数据模块编号',
  `action` varchar(2000) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作内容',
  `success` bit(1) NOT NULL DEFAULT b'1' COMMENT '操作结果',
  `extra` varchar(2000) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '拓展字段',
  `request_method` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '请求方法名',
  `request_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '请求地址',
  `user_ip` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户 IP',
  `user_agent` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '浏览器 UA',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9090 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志记录 V2 版本';

3. 使用案例实现步骤
    场景一：创建用户
    3.1 文件路径：cn/bztmaster/cnt/module/system/enums/LogRecordConstants.java
    在 LogRecordConstants类中，定义 SYSTEM_USER_CREATE_SUB_TYPE、SYSTEM_USER_CREATE_SUCCESS 变量。如：
        String SYSTEM_USER_CREATE_SUB_TYPE = "创建用户";
        String SYSTEM_USER_CREATE_SUCCESS = "创建了用户【{{#user.nickname}}】";
    3.2 在 Service 方法上，添加 @LogRecord 注解，如：
        类文件路径：cn/bztmaster/cnt/module/system/service/user/AdminUserServiceImpl.java
        在createUser 方法上添加注解：  
            @LogRecord(type = SYSTEM_USER_TYPE, subType = SYSTEM_USER_CREATE_SUB_TYPE, bizNo = "{{#user.id}}",success = SYSTEM_USER_CREATE_SUCCESS)
        然后在方法ruturn之前添加操作日志上下文：LogRecordContext.putVariable("user", user);
    场景二：修改用户
    3.3 文件路径：cn/bztmaster/cnt/module/system/enums/LogRecordConstants.java
    在 LogRecordConstants类中，定义 SYSTEM_USER_UPDATE_SUB_TYPE、SYSTEM_USER_UPDATE_SUCCESS 变量。如:
            String SYSTEM_USER_UPDATE_SUB_TYPE = "更新用户";
            String SYSTEM_USER_UPDATE_SUCCESS = "更新了用户【{{#user.nickname}}】: {_DIFF{#updateReqVO}}";
    _DIFF函数说明：实现对象 diff 功能，即“【备注】从【132】修改为【1324】”。因此，我们需要在 UserSaveReqVO类上添加 @DiffLogField 注解，如：
    UserSaveReqVO类文件路径：src/main/java/cn/bztmaster/cnt/module/system/controller/admin/user/vo/user/UserSaveReqVO.java
    注解的 name 字段：字段的中文名，例如说：“【备注】”
    注解的 function 字段：自定义函数，用于字段的值翻译，例如说：PostParseFunction岗位名、DeptParseFunction部门名、SexParseFunction性别等等
        PostParseFunction文件路径：src/main/java/cn/bztmaster/cnt/module/system/framework/operatelog/core/PostParseFunction.java
        DeptParseFunction文件路径：src/main/java/cn/bztmaster/cnt/module/system/framework/operatelog/
        SexParseFunction文件路径：src/main/java/cn/bztmaster/cnt/module/system/framework/operatelog/core/SexParseFunction.java
    3.4 在 Service 方法上，添加 @LogRecord 注解，如:
        类文件路径：cn/bztmaster/cnt/module/system/service/user/AdminUserServiceImpl.java
        在 updateUser 方法上添加注解：
            @LogRecord(type = SYSTEM_USER_TYPE, subType = SYSTEM_USER_UPDATE_SUB_TYPE, bizNo = "{{#updateReqVO.id}}",success = SYSTEM_USER_UPDATE_SUCCESS)
            方法后面添加记录操作日志上下文：
                LogRecordContext.putVariable(DiffParseFunction.OLD_OBJECT, BeanUtils.toBean(oldUser, UserSaveReqVO.class));
                LogRecordContext.putVariable("user", oldUser);
4. 实现合作伙伴的系统日志记录，要求如下：
   4.1 需要记录新增、更新、删除的日志，需要记录具体到某个字段的更新
   4.2 枚举值写入路径：src/main/java/cn/bztmaster/cnt/module/publicbiz/enums/LogRecordConstants.java
   4.3 需要添加代码注释
