package cn.bztmaster.cnt.module.publicbiz.enums;

import cn.bztmaster.cnt.framework.common.exception.ErrorCode;

/**
 * 公共业务模块的错误码枚举类
 *
 * 公共业务，使用 1-009-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 线索模块 1-009-001-000 ==========
    ErrorCode LEAD_NOT_EXISTS = new ErrorCode(1_009_001_000, "线索不存在");
    ErrorCode LEAD_ID_EXISTS = new ErrorCode(1_009_001_001, "线索ID已存在");
    ErrorCode LEAD_FOLLOW_UP_LOG_NOT_EXISTS = new ErrorCode(1_009_001_002, "线索跟进记录不存在");
    ErrorCode LEAD_CUSTOMER_PHONE_EXISTS = new ErrorCode(1_009_001_003, "客户联系电话已存在");

    // ========== 数字资产课程模块 1-009-002-000 ==========
    ErrorCode DIGITAL_ASSET_COURSE_NOT_EXISTS = new ErrorCode(1_009_002_000, "课程不存在");
    ErrorCode DIGITAL_ASSET_COURSE_NAME_DUPLICATE = new ErrorCode(1_009_002_001, "课程名称已存在");
    ErrorCode DIGITAL_ASSET_COURSE_STATUS_INVALID = new ErrorCode(1_009_002_002, "课程状态无效");
    ErrorCode DIGITAL_ASSET_COURSE_OFFLINE_NOT_SUPPORT_CHAPTER = new ErrorCode(1_009_002_003, "线下课程不支持章节课时管理");

    // ========== 课程章节模块 1-009-003-000 ==========
    ErrorCode COURSE_CHAPTER_NOT_EXISTS = new ErrorCode(1_009_003_000, "章节不存在");
    ErrorCode COURSE_CHAPTER_TITLE_DUPLICATE = new ErrorCode(1_009_003_001, "章节标题已存在");

    // ========== 课程课时模块 1-009-004-000 ==========
    ErrorCode COURSE_LESSON_NOT_EXISTS = new ErrorCode(1_009_004_000, "课时不存在");
    ErrorCode COURSE_LESSON_TITLE_DUPLICATE = new ErrorCode(1_009_004_001, "课时标题已存在");

    // ========== 课程附件模块 1-009-005-000 ==========
    ErrorCode COURSE_ATTACHMENT_NOT_EXISTS = new ErrorCode(1_009_005_000, "附件不存在");
    ErrorCode COURSE_ATTACHMENT_NAME_DUPLICATE = new ErrorCode(1_009_005_001, "附件名称已存在");

}