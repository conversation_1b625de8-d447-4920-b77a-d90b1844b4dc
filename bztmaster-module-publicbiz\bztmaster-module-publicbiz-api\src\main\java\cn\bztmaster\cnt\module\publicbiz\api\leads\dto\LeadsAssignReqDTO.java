package cn.bztmaster.cnt.module.publicbiz.api.leads.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 线索分配 Request DTO
 *
 * <AUTHOR>
 */
@Schema(description = "线索分配请求 DTO")
@Data
public class LeadsAssignReqDTO {

    @Schema(description = "线索主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "线索主键ID不能为空")
    private Long id;

    @Schema(description = "线索ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "LEAD20250721001")
    @Size(max = 32, message = "线索ID长度不能超过32")
    private String leadId;

    @Schema(description = "跟进人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2048")
    @NotNull(message = "跟进人ID不能为空")
    private Long userId;

    @Schema(description = "跟进人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "跟进人姓名不能为空")
    @Size(max = 64, message = "跟进人姓名长度不能超过64")
    private String userName;

    @Schema(description = "分配备注", example = "请尽快跟进该线索")
    private String assignRemark;
}