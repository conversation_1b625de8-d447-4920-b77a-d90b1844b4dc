package cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent;

import lombok.Data;
import java.util.Date;
import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;

/**
 * 认证与资质表实体
 * 对应表：talent_certificate
 */
@TableName("talent_certificate")
@KeySequence("talent_certificate_seq")
@Data
@EqualsAndHashCode(callSuper = true)
public class TalentCertificateDO extends BaseDO {
    @TableId
    private Long certificateId;
    private Long userId;
    private String name;
    private String issuer;
    private Date issueDate;
    private String source;
    private String status;
    private Boolean deleted;
    private String creator;
    private String updater;
    private Long tenantId;
    private String certificateNo;
    private Date expiryDate;
    private String certificateImageUrl;
}