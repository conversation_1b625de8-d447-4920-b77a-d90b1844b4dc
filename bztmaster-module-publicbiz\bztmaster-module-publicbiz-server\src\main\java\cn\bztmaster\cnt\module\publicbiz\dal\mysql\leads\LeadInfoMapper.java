package cn.bztmaster.cnt.module.publicbiz.dal.mysql.leads;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.leads.vo.LeadsPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadInfoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 线索信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LeadInfoMapper extends BaseMapperX<LeadInfoDO> {
    
    /**
     * 分页查询线索信息
     */
    default PageResult<LeadInfoDO> selectPage(LeadsPageReqVO reqVO) {
        LambdaQueryWrapperX<LeadInfoDO> wrapper = new LambdaQueryWrapperX<LeadInfoDO>()
                .eqIfPresent(LeadInfoDO::getId, reqVO.getId())
                .eqIfPresent(LeadInfoDO::getLeadId, reqVO.getLeadId())
                .likeIfPresent(LeadInfoDO::getCustomerName, reqVO.getCustomerName())
                .likeIfPresent(LeadInfoDO::getCustomerPhone, reqVO.getCustomerPhone())
                .eqIfPresent(LeadInfoDO::getLeadSource, reqVO.getLeadSource())
                .eqIfPresent(LeadInfoDO::getBusinessModule, reqVO.getBusinessModule())
                .eqIfPresent(LeadInfoDO::getLeadStatus, reqVO.getLeadStatus())
                .eqIfPresent(LeadInfoDO::getCreateMethod, reqVO.getCreateMethod())
                .likeIfPresent(LeadInfoDO::getCurrentOwner, reqVO.getCurrentOwner())
                .geIfPresent(LeadInfoDO::getCreateTime, reqVO.getBeginCreateTime())
                .leIfPresent(LeadInfoDO::getCreateTime, reqVO.getEndCreateTime());

        // 关键词搜索：模糊匹配客户姓名或客户手机号
        if (reqVO.getKeyword() != null && !reqVO.getKeyword().trim().isEmpty()) {
            wrapper.and(w -> w.like(LeadInfoDO::getCustomerName, reqVO.getKeyword())
                             .or()
                             .like(LeadInfoDO::getCustomerPhone, reqVO.getKeyword()));
        }

        return selectPage(reqVO, wrapper.orderByDesc(LeadInfoDO::getId));
    }
    
    /**
     * 根据线索ID查询线索信息
     */
    LeadInfoDO selectByLeadId(@Param("leadId") String leadId);
    
    /**
     * 根据客户电话查询线索信息
     */
    List<LeadInfoDO> selectListByCustomerPhone(@Param("customerPhone") String customerPhone);
    
    /**
     * 根据客户姓名模糊查询线索信息
     */
    List<LeadInfoDO> selectListByCustomerNameLike(@Param("customerName") String customerName);
    
    /**
     * 根据线索来源查询线索信息
     */
    List<LeadInfoDO> selectListByLeadSource(@Param("leadSource") Integer leadSource);
    
    /**
     * 根据业务模块查询线索信息
     */
    List<LeadInfoDO> selectListByBusinessModule(@Param("businessModule") Integer businessModule);
    
    /**
     * 根据线索状态查询线索信息
     */
    List<LeadInfoDO> selectListByLeadStatus(@Param("leadStatus") Integer leadStatus);
    
    /**
     * 根据当前跟进人查询线索信息
     */
    List<LeadInfoDO> selectListByCurrentOwner(@Param("currentOwner") String currentOwner);
}