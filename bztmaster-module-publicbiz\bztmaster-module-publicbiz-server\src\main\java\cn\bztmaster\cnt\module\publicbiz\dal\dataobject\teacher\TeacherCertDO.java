package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.teacher;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@TableName("publicbiz_teacher_cert")
@Schema(description = "师资库讲师资质表 DO")
public class TeacherCertDO {
    @TableId
    private Long id;
    private Long teacherId;
    private String certType;
    private String certName;
    private String fileName;
    private String fileUrl;
    private Date validStartDate;
    private Date validEndDate;
    private Date createTime;
    private Date updateTime;
    private String creator;
    private String updater;
    private Boolean deleted;
    private Long tenantId;
} 