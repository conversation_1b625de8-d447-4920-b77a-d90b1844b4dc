package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.employment.ServicePackageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/publicbiz/employment/service-package")
@Tag(name = "就业服务-服务套餐管理")
public class ServicePackageController {
    @Resource
    private ServicePackageService servicePackageService;

    @PostMapping("/create")
    @Operation(summary = "新增服务套餐")
    public CommonResult<Long> create(@RequestBody ServicePackageSaveReqVO reqVO) {
        return CommonResult.success(servicePackageService.createServicePackage(reqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新服务套餐")
    public CommonResult<Boolean> update(@RequestBody ServicePackageUpdateReqVO reqVO) {
        servicePackageService.updateServicePackage(reqVO);
        return CommonResult.success(true);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除服务套餐（软删除）")
    public CommonResult<Boolean> delete(@PathVariable("id") Long id) {
        servicePackageService.deleteServicePackage(id);
        return CommonResult.success(true);
    }

    @DeleteMapping("/{id}/move-to-recycle")
    @Operation(summary = "移动服务套餐到回收站")
    public CommonResult<Boolean> moveToRecycle(@PathVariable("id") Long id) {
        servicePackageService.moveServicePackageToRecycle(id);
        return CommonResult.success(true);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取服务套餐详情")
    public CommonResult<ServicePackageRespVO> get(@PathVariable("id") Long id) {
        return CommonResult.success(servicePackageService.getServicePackage(id));
    }

    @GetMapping("/page")
    @Operation(summary = "获取服务套餐分页")
    public CommonResult<PageResult<ServicePackageRespVO>> page(ServicePackagePageReqVO reqVO) {
        return CommonResult.success(servicePackageService.getServicePackagePage(reqVO));
    }

    @PutMapping("/status")
    @Operation(summary = "批量更新服务套餐状态")
    public CommonResult<Boolean> updateStatus(@RequestBody ServicePackageStatusUpdateReqVO reqVO) {
        servicePackageService.updateServicePackageStatus(reqVO);
        return CommonResult.success(true);
    }
} 