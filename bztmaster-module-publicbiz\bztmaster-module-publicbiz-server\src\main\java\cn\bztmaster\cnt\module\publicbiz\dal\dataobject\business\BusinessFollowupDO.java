package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.business;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@TableName("publicbiz_business_followup")
@Schema(description = "商机跟进表 DO")
public class BusinessFollowupDO {
    @TableId
    private Long id;
    private Long tenantId;
    private Long businessId;
    private String content;
    private Date followTime;
    private Long followUserId;
    private String followUserName;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private Boolean deleted;
} 