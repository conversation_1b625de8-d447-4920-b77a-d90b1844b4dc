package cn.bztmaster.cnt.module.publicbiz.api.business;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.api.business.dto.BusinessFollowupRespDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.*;

@FeignClient(name = "publicbiz-service")
@Tag(name = "RPC 服务 - 商机跟进")
public interface PublicBizBusinessFollowupApi {

    String PREFIX = "/publicbiz/business-followup";

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "通过跟进ID查询商机跟进")
    @Parameter(name = "id", description = "跟进编号", example = "1", required = true)
    CommonResult<BusinessFollowupRespDTO> getBusinessFollowup(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "通过跟进ID查询商机跟进们")
    @Parameter(name = "ids", description = "跟进编号数组", example = "1,2", required = true)
    CommonResult<List<BusinessFollowupRespDTO>> getBusinessFollowupList(@RequestParam("ids") Collection<Long> ids);

    // 可根据业务需求继续补充接口
} 