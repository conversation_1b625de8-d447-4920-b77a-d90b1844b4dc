package cn.bztmaster.cnt.module.system.api.material.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "管理后台 - 图片素材新增/更新 Request DTO")
public class ImageSaveReqDTO {
    @Schema(description = "图片ID", example = "1")
    private Long id;

    @Schema(description = "图片名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "图片名称不能为空")
    private String name;

    @Schema(description = "图片URL", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "图片URL不能为空")
    private String url;

    @Schema(description = "来源机构ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "来源机构ID不能为空")
    private Long sourceOrgId;

    @Schema(description = "来源机构名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "来源机构名称不能为空")
    private String sourceOrgName;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "可视范围机构ID")
    private Long visibleOrgId;

    @Schema(description = "可视范围机构名称")
    private String visibleOrgName;
} 