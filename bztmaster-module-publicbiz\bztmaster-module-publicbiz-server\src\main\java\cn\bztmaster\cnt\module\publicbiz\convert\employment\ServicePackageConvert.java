package cn.bztmaster.cnt.module.publicbiz.convert.employment;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ServicePackageConvert {
    ServicePackageConvert INSTANCE = Mappers.getMapper(ServicePackageConvert.class);

    ServicePackageDO convert(ServicePackageSaveReqVO bean);

    ServicePackageDO convert(ServicePackageUpdateReqVO bean);

    ServicePackageRespVO convert(ServicePackageDO bean);

    List<ServicePackageRespVO> convertList(List<ServicePackageDO> list);

    PageResult<ServicePackageRespVO> convertPage(PageResult<ServicePackageDO> page);
} 