package cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 课程章节 - Response VO
 * 对应前端页面：src/views/infra/ResourceCenter/DigitalAsset/components/ManagementCourseForOnline.vue
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "课程章节 - Response VO")
public class CourseChapterRespVO {

    @Schema(description = "章节ID", example = "1")
    private Long id;

    @Schema(description = "课程ID", example = "123")
    private Long courseId;

    @Schema(description = "章节标题", example = "SWOT分析法基础")
    private String title;

    @Schema(description = "排序序号", example = "1")
    private Integer sortOrder;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "该章节下的课时列表")
    private List<CourseLessonRespVO> lessons;
}
