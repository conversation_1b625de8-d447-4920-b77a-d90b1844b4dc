package cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent;

import lombok.Data;
import java.util.Date;
import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;

/**
 * 项目经历表实体
 * 对应表：talent_project
 */
@TableName("talent_project")
@KeySequence("talent_project_seq")
@Data
@EqualsAndHashCode(callSuper = true)
public class TalentProjectDO extends BaseDO {
    @TableId
    private Long projectId;
    private Long userId;
    private String name;
    private String description;
    private Date startDate;
    private Date endDate;
    private Boolean deleted;
    private String creator;
    private String updater;
    private Long tenantId;
}