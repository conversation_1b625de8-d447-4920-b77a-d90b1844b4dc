package cn.bztmaster.cnt.module.trade.framework.rpc.config;

import cn.bztmaster.cnt.module.member.api.address.MemberAddressApi;
import cn.bztmaster.cnt.module.member.api.config.MemberConfigApi;
import cn.bztmaster.cnt.module.member.api.level.MemberLevelApi;
import cn.bztmaster.cnt.module.member.api.point.MemberPointApi;
import cn.bztmaster.cnt.module.member.api.user.MemberUserApi;
import cn.bztmaster.cnt.module.pay.api.order.PayOrderApi;
import cn.bztmaster.cnt.module.pay.api.refund.PayRefundApi;
import cn.bztmaster.cnt.module.pay.api.transfer.PayTransferApi;
import cn.bztmaster.cnt.module.pay.api.wallet.PayWalletApi;
import cn.bztmaster.cnt.module.product.api.category.ProductCategoryApi;
import cn.bztmaster.cnt.module.product.api.comment.ProductCommentApi;
import cn.bztmaster.cnt.module.product.api.sku.ProductSkuApi;
import cn.bztmaster.cnt.module.product.api.spu.ProductSpuApi;
import cn.bztmaster.cnt.module.promotion.api.bargain.BargainActivityApi;
import cn.bztmaster.cnt.module.promotion.api.bargain.BargainRecordApi;
import cn.bztmaster.cnt.module.promotion.api.combination.CombinationRecordApi;
import cn.bztmaster.cnt.module.promotion.api.coupon.CouponApi;
import cn.bztmaster.cnt.module.promotion.api.discount.DiscountActivityApi;
import cn.bztmaster.cnt.module.promotion.api.point.PointActivityApi;
import cn.bztmaster.cnt.module.promotion.api.reward.RewardActivityApi;
import cn.bztmaster.cnt.module.promotion.api.seckill.SeckillActivityApi;
import cn.bztmaster.cnt.module.system.api.notify.NotifyMessageSendApi;
import cn.bztmaster.cnt.module.system.api.social.SocialClientApi;
import cn.bztmaster.cnt.module.system.api.social.SocialUserApi;
import cn.bztmaster.cnt.module.system.api.user.AdminUserApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(value = "tradeRpcConfiguration", proxyBeanMethods = false)
@EnableFeignClients(clients = {
        BargainActivityApi.class, BargainRecordApi.class, CombinationRecordApi.class,
        CouponApi.class, DiscountActivityApi.class, RewardActivityApi.class, SeckillActivityApi.class, PointActivityApi.class,
        MemberUserApi.class, MemberPointApi.class, MemberLevelApi.class, MemberAddressApi.class, MemberConfigApi.class,
        ProductSpuApi.class, ProductSkuApi.class, ProductCommentApi.class, ProductCategoryApi.class,
        PayOrderApi.class, PayRefundApi.class, PayTransferApi.class, PayWalletApi.class,
        AdminUserApi.class, NotifyMessageSendApi.class, SocialClientApi.class, SocialUserApi.class
})
public class RpcConfiguration {
}
