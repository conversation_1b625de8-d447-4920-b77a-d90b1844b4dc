package cn.bztmaster.cnt.module.talentpool.api.talent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;
import java.util.List;
import java.math.BigDecimal;

@Data
public class TalentUserCreateReqDTO {
    // 主表字段
    @Schema(description = "姓名")
    private String name;
    @Schema(description = "手机号")
    private String phone;
    @Schema(description = "身份证号")
    private String identityId;
    @Schema(description = "邮箱")
    private String email;
    @Schema(description = "性别")
    private String gender;
    @Schema(description = "出生日期")
    private Date birthDate;
    @Schema(description = "头像URL")
    private String avatarUrl;
    @Schema(description = "用户来源")
    private String source;
    @Schema(description = "用户标签")
    private List<String> tags;
    @Schema(description = "用户状态")
    private String status;
    @Schema(description = "档案完整度")
    private Integer completeness;
    @Schema(description = "所属机构ID")
    private Long orgId;
    @Schema(description = "所属机构名称")
    private String orgName;
    @Schema(description = "人才来源")
    private String talentSource;
    @Schema(description = "是否为平台自营（0-否，1-是）")
    private Boolean isSelfSupport;

    // 子表
    @Schema(description = "教育背景列表")
    private List<EducationDTO> educationList;
    @Schema(description = "校园实践列表")
    private List<CampusPracticeDTO> campusPracticeList;
    @Schema(description = "实习经历列表")
    private List<InternshipDTO> internshipList;
    @Schema(description = "项目经历列表")
    private List<ProjectDTO> projectList;
    @Schema(description = "培训经历列表")
    private List<TrainingDTO> trainingList;
    @Schema(description = "技能列表")
    private List<SkillDTO> skillList;
    @Schema(description = "证书列表")
    private List<CertificateDTO> certificateList;
    @Schema(description = "求职申请列表")
    private List<JobApplicationDTO> jobApplicationList;
    @Schema(description = "工作经历列表")
    private List<EmploymentDTO> employmentList;
    @Schema(description = "用户标签明细列表")
    private List<UserTagDTO> userTagList;

    // 子表DTO定义
    @Data
    public static class EducationDTO {
        private String institution;
        private String collegeAddress;
        private String degreeType;
        private String major;
        private Date startDate;
        private Date endDate;
        private String academicRanking;
        private Integer isInternship;
        private String internshipType;
        private Integer internshipDuration;
    }

    @Data
    public static class CampusPracticeDTO {
        private String practiceName;
        private String organizer;
        private Date startDate;
        private Date endDate;
        private String practiceReport;
    }

    @Data
    public static class InternshipDTO {
        private String company;
        private String position;
        private Date startDate;
        private Date endDate;
        private String responsibilities;
    }

    @Data
    public static class ProjectDTO {
        private String name;
        private String description;
        private Date startDate;
        private Date endDate;
    }

    @Data
    public static class TrainingDTO {
        private String provider;
        private String course;
        private Date completeDate;
    }

    @Data
    public static class SkillDTO {
        private String name;
        private String level;
    }

    @Data
    public static class CertificateDTO {
        private String name;
        private String issuer;
        private Date issueDate;
        private String source;
        private String status;
        private String certificateNo;
        private Date expiryDate;
        private String certificateImageUrl;
    }

    @Data
    public static class JobApplicationDTO {
        private String company;
        private String position;
        private Date applyDate;
        private String status;
    }

    @Data
    public static class EmploymentDTO {
        private String company;
        private String position;
        private Date startDate;
        private Date endDate;
        private BigDecimal salary;
    }

    @Data
    public static class UserTagDTO {
        private Long tagId;
        private Long tagTypeId;
        private String tagName;
        private String tagTypeName;
    }
}