package cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent;

import lombok.Data;
import java.util.Date;
import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;

/**
 * 教育背景表实体
 * 对应表：talent_education
 */
@TableName("talent_education")
@KeySequence("talent_education_seq")
@Data
@EqualsAndHashCode(callSuper = true)
public class TalentEducationDO extends BaseDO {
    @TableId
    private Long educationId;
    private Long userId;
    private String institution;
    private String collegeAddress;
    private String degreeType;
    private String major;
    private Date startDate;
    private Date endDate;
    private String academicRanking;
    private Integer isInternship;
    private String internshipType;
    private Integer internshipDuration;
    private Boolean deleted;
    private String creator;
    private String updater;
    private Long tenantId;
}