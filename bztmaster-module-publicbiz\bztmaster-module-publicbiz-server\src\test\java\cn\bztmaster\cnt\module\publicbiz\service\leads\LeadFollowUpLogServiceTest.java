package cn.bztmaster.cnt.module.publicbiz.service.leads;

import cn.bztmaster.cnt.framework.common.exception.ServiceException;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.test.core.ut.BaseDbUnitTest;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.LeadFollowUpLogPageReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.LeadFollowUpLogSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.leads.vo.LeadFollowUpLogPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadFollowUpLogDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadInfoDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.leads.LeadFollowUpLogMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.leads.LeadInfoMapper;
import cn.bztmaster.cnt.module.publicbiz.service.leads.impl.LeadFollowUpLogServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * {@link LeadFollowUpLogServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
class LeadFollowUpLogServiceTest extends BaseDbUnitTest {

    @InjectMocks
    private LeadFollowUpLogServiceImpl leadFollowUpLogService;

    @Mock
    private LeadFollowUpLogMapper leadFollowUpLogMapper;

    @Mock
    private LeadInfoMapper leadInfoMapper;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCreateLeadFollowUpLog_success() {
        // 准备参数
        LeadFollowUpLogSaveReqDTO reqDTO = new LeadFollowUpLogSaveReqDTO();
        reqDTO.setLeadId("LEAD20250721001");
        reqDTO.setFollowUpContent("已电话联系客户，客户表示有意向");

        // mock 方法
        LeadInfoDO existingLead = new LeadInfoDO();
        existingLead.setId(1L);
        existingLead.setLeadId(reqDTO.getLeadId());
        when(leadInfoMapper.selectByLeadId(reqDTO.getLeadId())).thenReturn(existingLead);
        
        when(leadFollowUpLogMapper.insert(any(LeadFollowUpLogDO.class))).thenAnswer(invocation -> {
            LeadFollowUpLogDO logDO = invocation.getArgument(0);
            logDO.setId(1L);
            return 1;
        });

        // 调用
        Long logId = leadFollowUpLogService.createLeadFollowUpLog(reqDTO);

        // 断言
        assertEquals(1L, logId);
        
        // 验证 leadFollowUpLogMapper 的调用
        ArgumentCaptor<LeadFollowUpLogDO> logCaptor = ArgumentCaptor.forClass(LeadFollowUpLogDO.class);
        verify(leadFollowUpLogMapper).insert(logCaptor.capture());
        
        LeadFollowUpLogDO capturedLog = logCaptor.getValue();
        assertEquals(reqDTO.getLeadId(), capturedLog.getLeadId());
        assertEquals(reqDTO.getFollowUpContent(), capturedLog.getFollowUpContent());
    }

    @Test
    void testCreateLeadFollowUpLog_leadNotExists() {
        // 准备参数
        LeadFollowUpLogSaveReqDTO reqDTO = new LeadFollowUpLogSaveReqDTO();
        reqDTO.setLeadId("LEAD20250721001");
        reqDTO.setFollowUpContent("已电话联系客户，客户表示有意向");

        // mock 方法 - 模拟线索不存在
        when(leadInfoMapper.selectByLeadId(reqDTO.getLeadId())).thenReturn(null);

        // 调用，并断言异常
        ServiceException exception = assertThrows(ServiceException.class, 
                () -> leadFollowUpLogService.createLeadFollowUpLog(reqDTO));
        assertEquals(LEAD_NOT_EXISTS.getCode(), exception.getCode());
    }

    @Test
    void testUpdateLeadFollowUpLog_success() {
        // 准备参数
        LeadFollowUpLogSaveReqDTO reqDTO = new LeadFollowUpLogSaveReqDTO();
        reqDTO.setId(1L);
        reqDTO.setLeadId("LEAD20250721001");
        reqDTO.setFollowUpContent("更新跟进内容：客户已确认购买意向");

        // mock 方法
        LeadFollowUpLogDO existingLog = new LeadFollowUpLogDO();
        existingLog.setId(1L);
        existingLog.setLeadId(reqDTO.getLeadId());
        when(leadFollowUpLogMapper.selectById(reqDTO.getId())).thenReturn(existingLog);
        
        LeadInfoDO existingLead = new LeadInfoDO();
        existingLead.setId(1L);
        existingLead.setLeadId(reqDTO.getLeadId());
        when(leadInfoMapper.selectByLeadId(reqDTO.getLeadId())).thenReturn(existingLead);
        
        when(leadFollowUpLogMapper.updateById(any(LeadFollowUpLogDO.class))).thenReturn(1);

        // 调用
        leadFollowUpLogService.updateLeadFollowUpLog(reqDTO);

        // 验证 leadFollowUpLogMapper 的调用
        ArgumentCaptor<LeadFollowUpLogDO> logCaptor = ArgumentCaptor.forClass(LeadFollowUpLogDO.class);
        verify(leadFollowUpLogMapper).updateById(logCaptor.capture());
        
        LeadFollowUpLogDO capturedLog = logCaptor.getValue();
        assertEquals(reqDTO.getId(), capturedLog.getId());
        assertEquals(reqDTO.getLeadId(), capturedLog.getLeadId());
        assertEquals(reqDTO.getFollowUpContent(), capturedLog.getFollowUpContent());
    }

    @Test
    void testUpdateLeadFollowUpLog_logNotExists() {
        // 准备参数
        LeadFollowUpLogSaveReqDTO reqDTO = new LeadFollowUpLogSaveReqDTO();
        reqDTO.setId(1L);
        reqDTO.setLeadId("LEAD20250721001");
        reqDTO.setFollowUpContent("更新跟进内容");

        // mock 方法 - 模拟跟进记录不存在
        when(leadFollowUpLogMapper.selectById(reqDTO.getId())).thenReturn(null);

        // 调用，并断言异常
        ServiceException exception = assertThrows(ServiceException.class, 
                () -> leadFollowUpLogService.updateLeadFollowUpLog(reqDTO));
        assertEquals(LEAD_FOLLOW_UP_LOG_NOT_EXISTS.getCode(), exception.getCode());
    }

    @Test
    void testUpdateLeadFollowUpLog_leadNotExists() {
        // 准备参数
        LeadFollowUpLogSaveReqDTO reqDTO = new LeadFollowUpLogSaveReqDTO();
        reqDTO.setId(1L);
        reqDTO.setLeadId("LEAD20250721001");
        reqDTO.setFollowUpContent("更新跟进内容");

        // mock 方法
        LeadFollowUpLogDO existingLog = new LeadFollowUpLogDO();
        existingLog.setId(1L);
        existingLog.setLeadId(reqDTO.getLeadId());
        when(leadFollowUpLogMapper.selectById(reqDTO.getId())).thenReturn(existingLog);
        
        // 模拟线索不存在
        when(leadInfoMapper.selectByLeadId(reqDTO.getLeadId())).thenReturn(null);

        // 调用，并断言异常
        ServiceException exception = assertThrows(ServiceException.class, 
                () -> leadFollowUpLogService.updateLeadFollowUpLog(reqDTO));
        assertEquals(LEAD_NOT_EXISTS.getCode(), exception.getCode());
    }

    @Test
    void testDeleteLeadFollowUpLog_success() {
        // 准备参数
        Long id = 1L;

        // mock 方法
        LeadFollowUpLogDO existingLog = new LeadFollowUpLogDO();
        existingLog.setId(id);
        when(leadFollowUpLogMapper.selectById(id)).thenReturn(existingLog);
        when(leadFollowUpLogMapper.deleteById(id)).thenReturn(1);

        // 调用
        leadFollowUpLogService.deleteLeadFollowUpLog(id);

        // 验证 leadFollowUpLogMapper 的调用
        verify(leadFollowUpLogMapper).deleteById(id);
    }

    @Test
    void testDeleteLeadFollowUpLog_notExists() {
        // 准备参数
        Long id = 1L;

        // mock 方法 - 模拟跟进记录不存在
        when(leadFollowUpLogMapper.selectById(id)).thenReturn(null);

        // 调用，并断言异常
        ServiceException exception = assertThrows(ServiceException.class, 
                () -> leadFollowUpLogService.deleteLeadFollowUpLog(id));
        assertEquals(LEAD_FOLLOW_UP_LOG_NOT_EXISTS.getCode(), exception.getCode());
    }

    @Test
    void testGetLeadFollowUpLog() {
        // 准备参数
        Long id = 1L;

        // mock 方法
        LeadFollowUpLogDO log = new LeadFollowUpLogDO();
        log.setId(id);
        log.setLeadId("LEAD20250721001");
        log.setFollowUpContent("已电话联系客户");
        when(leadFollowUpLogMapper.selectById(id)).thenReturn(log);

        // 调用
        LeadFollowUpLogDO result = leadFollowUpLogService.getLeadFollowUpLog(id);

        // 断言
        assertNotNull(result);
        assertEquals(id, result.getId());
        assertEquals("LEAD20250721001", result.getLeadId());
        assertEquals("已电话联系客户", result.getFollowUpContent());
    }

    @Test
    void testGetLeadFollowUpLogListByLeadId() {
        // 准备参数
        String leadId = "LEAD20250721001";

        // mock 方法
        List<LeadFollowUpLogDO> logList = new ArrayList<>();
        LeadFollowUpLogDO log1 = new LeadFollowUpLogDO();
        log1.setId(1L);
        log1.setLeadId(leadId);
        log1.setFollowUpContent("第一次跟进");
        log1.setCreateTime(LocalDateTime.now().minusDays(2));
        logList.add(log1);
        
        LeadFollowUpLogDO log2 = new LeadFollowUpLogDO();
        log2.setId(2L);
        log2.setLeadId(leadId);
        log2.setFollowUpContent("第二次跟进");
        log2.setCreateTime(LocalDateTime.now().minusDays(1));
        logList.add(log2);
        
        when(leadFollowUpLogMapper.selectListByLeadId(leadId)).thenReturn(logList);

        // 调用
        List<LeadFollowUpLogDO> result = leadFollowUpLogService.getLeadFollowUpLogListByLeadId(leadId);

        // 断言
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("第一次跟进", result.get(0).getFollowUpContent());
        assertEquals("第二次跟进", result.get(1).getFollowUpContent());
    }

    @Test
    void testGetLeadFollowUpLogPage() {
        // 准备参数
        LeadFollowUpLogPageReqDTO pageReqDTO = new LeadFollowUpLogPageReqDTO();
        pageReqDTO.setLeadId("LEAD20250721001");
        pageReqDTO.setPageNo(1);
        pageReqDTO.setPageSize(10);
        pageReqDTO.setBeginCreateTime(LocalDateTime.now().minusDays(7).toString());
        pageReqDTO.setEndCreateTime(LocalDateTime.now().toString());

        // mock 方法
        List<LeadFollowUpLogDO> logList = new ArrayList<>();
        LeadFollowUpLogDO log = new LeadFollowUpLogDO();
        log.setId(1L);
        log.setLeadId(pageReqDTO.getLeadId());
        log.setFollowUpContent("跟进内容");
        logList.add(log);
        
        PageResult<LeadFollowUpLogDO> pageResult = new PageResult<>();
        pageResult.setList(logList);
        pageResult.setTotal(1L);
        
        when(leadFollowUpLogMapper.selectPage(any(LeadFollowUpLogPageReqVO.class))).thenReturn(pageResult);

        // 调用
        PageResult<LeadFollowUpLogDO> result = leadFollowUpLogService.getLeadFollowUpLogPage(pageReqDTO);

        // 断言
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getList().size());
        assertEquals("跟进内容", result.getList().get(0).getFollowUpContent());
    }

    @Test
    void testDeleteLeadFollowUpLogByLeadId() {
        // 准备参数
        String leadId = "LEAD20250721001";

        // mock 方法
        doNothing().when(leadFollowUpLogMapper).deleteByLeadId(leadId);

        // 调用
        leadFollowUpLogService.deleteLeadFollowUpLogByLeadId(leadId);

        // 验证 leadFollowUpLogMapper 的调用
        verify(leadFollowUpLogMapper).deleteByLeadId(leadId);
    }
}