package cn.bztmaster.cnt.module.publicbiz.convert.employment;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageFeatureDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ServicePackageFeatureConvert {
    ServicePackageFeatureConvert INSTANCE = Mappers.getMapper(ServicePackageFeatureConvert.class);

    ServicePackageFeatureDO convert(ServicePackageFeatureSaveReqVO bean);

    ServicePackageFeatureRespVO convert(ServicePackageFeatureDO bean);

    List<ServicePackageFeatureRespVO> convertList(List<ServicePackageFeatureDO> list);
} 