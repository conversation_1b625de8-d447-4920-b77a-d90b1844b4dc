#set ($subTable = $subTables.get($subIndex))##当前表
#set ($subColumns = $subColumnsList.get($subIndex))##当前字段数组
package ${basePackage}.module.${subTable.moduleName}.dal.dataobject.${subTable.businessName};

import lombok.*;
import java.util.*;
#foreach ($column in $subColumns)
#if (${column.javaType} == "BigDecimal")
import java.math.BigDecimal;
#end
#if (${column.javaType} == "LocalDateTime")
import java.time.LocalDateTime;
#end
#end
import com.baomidou.mybatisplus.annotation.*;
import ${BaseDOClassName};
## 处理 Schema 注解（仅 DO 模式）
#if ($voType == 20)
import io.swagger.v3.oas.annotations.media.Schema;
#foreach ($column in $columns)
    #if ("$!column.dictType" != "")## 有设置数据字典
        import ${DictFormatClassName};
        import ${DictConvertClassName};
        #break
    #end
#end
#end

/**
 * ${subTable.classComment} DO
 *
 * <AUTHOR>
 */
@TableName("${subTable.tableName.toLowerCase()}")
@KeySequence("${subTable.tableName.toLowerCase()}_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
## 处理 Schema 注解（仅 DO 模式）
#if ($voType == 20)
@Schema(description = "${sceneEnum.name} - ${table.classComment} Response VO")
#end
public class ${subTable.className}DO extends BaseDO {

#foreach ($column in $subColumns)
#if (!${baseDOFields.contains(${column.javaField})})##排除 BaseDO 的字段
    /**
     * ${column.columnComment}
    #if ("$!column.dictType" != "")##处理枚举值
     *
     * 枚举 {@link TODO ${column.dictType} 对应的类}
    #end
     */
    #if (${column.primaryKey})##处理主键
    @TableId#if (${column.javaType} == 'String')(type = IdType.INPUT)#end
    #end
#if ($voType == 20)
## 1. 处理 Swagger 注解
    @Schema(description = "${column.columnComment}"#if (!${column.nullable}), requiredMode = Schema.RequiredMode.REQUIRED#end#if ("$!column.example" != ""), example = "${column.example}"#end)
#end
## 2. 处理字段定义
    private ${column.javaType} ${column.javaField};
#end
#end

}