package cn.bztmaster.cnt.module.publicbiz.enums.leads;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 线索来源枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum LeadSourceEnum {

    WEBSITE_REGISTRATION(1, "官网注册"),
    MARKETING_ACTIVITY(2, "市场活动"),
    WECHAT_ARTICLE(3, "公众号文章"),
    VIDEO_ACCOUNT(4, "视频号"),
    TIKTOK(5, "抖音"),
    REFERRAL(6, "客户推荐"),
    COLD_CALL(7, "电话营销"),
    SOCIAL_MEDIA(8, "社交媒体"),
    EXHIBITION(9, "展会"),
    OTHER(99, "其他");

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据类型获取枚举
     *
     * @param type 类型
     * @return 枚举
     */
    public static LeadSourceEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (LeadSourceEnum value : LeadSourceEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

}