package cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;

/**
 * 用户评价表 DO
 * 对应表：talent_user_comment
 */
@TableName("talent_user_comment")
@KeySequence("talent_user_comment_seq")
@Data
public class TalentUserCommentDO {
    @TableId
    private Long commentId;
    private Long userId;
    private String role;
    private String reviewerName;
    private String serviceType;
    private String serviceName;
    private String content;
    private Integer score;
    private Boolean deleted;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private Long tenantId;
}