-- 新增表信息
服务套餐主表：publicbiz_service_package
服务套餐轮播图表：publicbiz_package_carousel




-- 服务套餐主表
CREATE TABLE `publicbiz_service_package` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 业务字段
  `name` VARCHAR(200) NOT NULL COMMENT '套餐名称',
  `category` VARCHAR(50) NOT NULL COMMENT '服务分类：日常保洁/深度保洁/家电清洗/专项服务/月嫂服务/收纳整理',
  `thumbnail` VARCHAR(500) COMMENT '套餐主图URL',
  `price` DECIMAL(10,2) NOT NULL COMMENT '套餐价格',
  `original_price` DECIMAL(10,2) COMMENT '原价',
  `unit` VARCHAR(20) NOT NULL COMMENT '价格单位：次/项/天/月',
  `service_duration` VARCHAR(100) COMMENT '服务时长，如：4小时、26天、90天',
  `package_type` VARCHAR(20) NOT NULL COMMENT '套餐类型：long-term-长周期套餐/count-card-次数次卡套餐',
  `task_split_rule` VARCHAR(200) COMMENT '任务拆分规则',
  `service_description` TEXT COMMENT '服务描述，建议100-200字',
  `service_details` LONGTEXT COMMENT '详细服务内容，富文本格式',
  `service_process` LONGTEXT COMMENT '服务流程，富文本格式',
  `purchase_notice` TEXT COMMENT '购买须知',
  `status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '状态：active-已上架/pending-待上架/deleted-回收站',
  
  -- 预约配置字段
  `advance_booking_days` INT NOT NULL DEFAULT 1 COMMENT '预约时间范围：1-提前1天/3-提前3天/7-提前7天',
  `time_selection_mode` VARCHAR(20) NOT NULL DEFAULT 'fixed' COMMENT '时间选择模式：fixed-固定时间/flexible-灵活时间',
  `appointment_mode` VARCHAR(20) NOT NULL DEFAULT 'start-date' COMMENT '预约模式：start-date-开始日期预约/all-at-once-一次性预约全部服务次数',
  `service_start_time` VARCHAR(20) NOT NULL DEFAULT 'within-3-days' COMMENT '服务开始时间：within-3-days-下单后3天内开始/specified-date-指定日期开始',
  `address_setting` VARCHAR(20) NOT NULL DEFAULT 'fixed' COMMENT '地址设置：fixed-固定地址/changeable-可变更地址',
  `max_booking_days` INT NOT NULL DEFAULT 30 COMMENT '最大预约天数',
  `cancellation_policy` VARCHAR(500) COMMENT '取消政策',
  
  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`),
  KEY `idx_package_type` (`package_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='服务套餐主表';

-- 服务套餐轮播图表
CREATE TABLE `publicbiz_package_carousel` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 业务字段
  `package_id` BIGINT NOT NULL COMMENT '套餐ID',
  `image_url` VARCHAR(500) NOT NULL COMMENT '轮播图URL',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序，数字越小越靠前',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  
  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_package_id` (`package_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='服务套餐轮播图表';


-- 服务套餐特色标签表
CREATE TABLE `publicbiz_package_feature` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 业务字段
  `package_id` BIGINT NOT NULL COMMENT '套餐ID',
  `feature_name` VARCHAR(100) NOT NULL COMMENT '特色标签名称',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序，数字越小越靠前',
  
  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_package_id` (`package_id`),
  KEY `idx_feature_name` (`feature_name`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='服务套餐特色标签表';