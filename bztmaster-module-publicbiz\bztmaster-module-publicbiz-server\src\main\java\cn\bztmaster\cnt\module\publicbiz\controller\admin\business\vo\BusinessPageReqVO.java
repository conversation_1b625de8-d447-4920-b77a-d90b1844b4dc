package cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "商机中心 - 商机分页 Request VO")
public class BusinessPageReqVO extends PageParam {
    private Long id;
    private Long tenantId;
    private String name;
    private Long customerId;
    private String customerName;
    private String businessType;
    private BigDecimal totalPrice;
    private Date expectedDealDate;
    private String businessStage;
    private String description;
    private Long ownerUserId;
    private String ownerUserName;
    private String creator;
    private String updater;
    private Boolean deleted;
} 