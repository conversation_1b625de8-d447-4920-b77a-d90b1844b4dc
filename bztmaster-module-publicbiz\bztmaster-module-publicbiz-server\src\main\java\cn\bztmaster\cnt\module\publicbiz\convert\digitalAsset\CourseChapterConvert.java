package cn.bztmaster.cnt.module.publicbiz.convert.digitalAsset;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset.CourseChapterDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
 * 课程章节 Convert
 * 
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface CourseChapterConvert {
    
    CourseChapterConvert INSTANCE = Mappers.getMapper(CourseChapterConvert.class);

    /**
     * VO 转 DO
     */
    CourseChapterDO convert(CourseChapterSaveReqVO bean);

    /**
     * DO 转 VO
     */
    CourseChapterRespVO convert(CourseChapterDO bean);

    /**
     * DO 列表转 VO 列表
     */
    List<CourseChapterRespVO> convertList(List<CourseChapterDO> list);
}
