package cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@Schema(description = "资源中心 - 合作伙伴 Response VO")
public class PartnerRespVO {
    @Schema(description = "主键ID")
    private Long id;
    @Schema(description = "机构名称")
    private String name;
    @Schema(description = "机构类型")
    private String type;
    @Schema(description = "业务模块")
    private String biz;
    @Schema(description = "合作状态")
    private String status;
    @Schema(description = "风险等级")
    private String risk;
    @Schema(description = "我方负责人ID")
    private Long owner;
    @Schema(description = "我方负责人昵称")
    private String ownerName;
    @Schema(description = "租户ID")
    private Long tenantId;
    @Schema(description = "创建者")
    private String creator;
    @Schema(description = "更新者")
    private String updater;
    @Schema(description = "是否删除")
    private Boolean deleted;
    @Schema(description = "创建时间")
    private String createTime;
    @Schema(description = "更新时间")
    private String updateTime;
    @Schema(description = "机构简称")
    private String shortName;
    @Schema(description = "法人代表")
    private String legalPerson;
    @Schema(description = "成立日期")
    private String foundationDate;
    @Schema(description = "统一社会信用代码")
    private String creditCode;
    @Schema(description = "注册地址")
    private String registerAddress;
    @Schema(description = "经营地址")
    private String businessAddress;
    @Schema(description = "主营业务")
    private String mainBusiness;
    @Schema(description = "主要联系人")
    private String contactName;
    @Schema(description = "联系电话")
    private String contactPhone;
    @Schema(description = "当前评级（星级）")
    private Integer rating;
    @Schema(description = "合作模式")
    private String cooperationMode;
    @Schema(description = "合同编号")
    private String contractNo;
    @Schema(description = "合同开始日期")
    private String contractStart;
    @Schema(description = "合同结束日期")
    private String contractEnd;
    @Schema(description = "保证金")
    private java.math.BigDecimal deposit;
    @Schema(description = "续约提醒日期")
    private String renewDate;
    @Schema(description = "对公账户名")
    private String accountName;
    @Schema(description = "结算周期")
    private String settlementCycle;
    @Schema(description = "开户银行")
    private String bankName;
    @Schema(description = "银行账号")
    private String bankAccount;
    @Schema(description = "资质文件（URL或ID）")
    private String qualificationFile;
    @Schema(description = "开票类型")
    private String invoiceType;
    @Schema(description = "开票名称")
    private String invoiceName;
    @Schema(description = "纳税人识别号")
    private String taxId;
    @Schema(description = "社会组织代码")
    private String orgCode;
    @Schema(description = "开票地址")
    private String invoiceAddress;
    @Schema(description = "开票电话")
    private String invoicePhone;
    @Schema(description = "开票开户银行")
    private String invoiceBank;
    @Schema(description = "开票银行账号")
    private String invoiceBankAccount;
    @Schema(description = "开票邮箱")
    private String invoiceEmail;
    @Schema(description = "开票联系人")
    private String invoiceContact;
    @Schema(description = "开票资质文件（URL或ID）")
    private String invoiceQualificationFile;
    @Schema(description = "开票备注")
    private String invoiceRemark;
}
