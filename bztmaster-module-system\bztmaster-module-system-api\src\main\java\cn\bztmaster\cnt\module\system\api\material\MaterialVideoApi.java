package cn.bztmaster.cnt.module.system.api.material;

import cn.hutool.core.convert.Convert;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.util.collection.CollectionUtils;
import cn.bztmaster.cnt.module.system.api.material.dto.VideoRespDTO;
import cn.bztmaster.cnt.module.system.enums.ApiConstants;
import com.fhs.core.trans.anno.AutoTrans;
import com.fhs.trans.service.AutoTransable;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.*;

import static cn.bztmaster.cnt.module.system.api.material.MaterialVideoApi.PREFIX;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 视频素材")
@AutoTrans(namespace = PREFIX, fields = {"name"})
public interface MaterialVideoApi extends AutoTransable<VideoRespDTO> {

    String PREFIX = ApiConstants.PREFIX + "/material/video";

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "通过视频 ID 查询视频")
    @Parameter(name = "id", description = "视频编号", example = "1", required = true)
    CommonResult<VideoRespDTO> getVideo(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "通过视频 ID 查询视频们")
    @Parameter(name = "ids", description = "视频编号数组", example = "1,2", required = true)
    CommonResult<List<VideoRespDTO>> getVideoList(@RequestParam("ids") Collection<Long> ids);

    @GetMapping(PREFIX + "/list-by-category")
    @Operation(summary = "通过分类 ID 查询视频们")
    @Parameter(name = "categoryId", description = "分类编号", example = "1", required = true)
    CommonResult<List<VideoRespDTO>> getVideoListByCategoryId(@RequestParam("categoryId") Long categoryId);

    @GetMapping(PREFIX + "/list-by-org")
    @Operation(summary = "通过来源机构 ID 查询视频们")
    @Parameter(name = "sourceOrgId", description = "来源机构编号", example = "1", required = true)
    CommonResult<List<VideoRespDTO>> getVideoListBySourceOrgId(@RequestParam("sourceOrgId") Long sourceOrgId);

    @GetMapping(PREFIX + "/recycleList")
    @Operation(summary = "视频回收站列表")
    CommonResult<List<VideoRespDTO>> getVideoRecycleList(@RequestParam Map<String, Object> params);

    @PostMapping(PREFIX + "/recycleRestore")
    @Operation(summary = "视频回收站恢复")
    CommonResult<Boolean> recycleRestore(@RequestBody List<Long> idList);

    @PostMapping(PREFIX + "/recycleDelete")
    @Operation(summary = "视频回收站永久删除")
    CommonResult<Boolean> recycleDelete(@RequestBody List<Long> idList);

    /**
     * 获得视频 Map
     *
     * @param ids 视频编号数组
     * @return 视频 Map
     */
    default Map<Long, VideoRespDTO> getVideoMap(Collection<Long> ids) {
        List<VideoRespDTO> videos = getVideoList(ids).getCheckedData();
        return CollectionUtils.convertMap(videos, VideoRespDTO::getId);
    }

    /**
     * 校验视频是否有效。如下情况，视为无效：
     * 1. 视频编号不存在
     * 2. 视频被禁用
     *
     * @param id 视频编号
     */
    default void validateVideo(Long id) {
        validateVideoList(Collections.singleton(id));
    }

    @GetMapping(PREFIX + "/valid")
    @Operation(summary = "校验视频们是否有效")
    @Parameter(name = "ids", description = "视频编号数组", example = "3,5", required = true)
    CommonResult<Boolean> validateVideoList(@RequestParam("ids") Collection<Long> ids);

    @Override
    @GetMapping("select")
    default List<VideoRespDTO> selectByIds(List<?> ids) {
        return getVideoList(Convert.toList(Long.class, ids)).getCheckedData();
    }

    @Override
    @GetMapping("select-list")
    default VideoRespDTO selectById(Object id) {
        return getVideo(Convert.toLong(id)).getCheckedData();
    }
} 