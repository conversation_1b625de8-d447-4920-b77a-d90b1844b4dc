package cn.bztmaster.cnt.module.system.api.material.dto;

import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "素材分类 - 列表响应 DTO")
public class CategoryListRespDTO implements VO {
    @Schema(description = "分类ID", example = "1")
    private Long id;

    @Schema(description = "分类名称", example = "图片素材")
    private String name;

    @Schema(description = "父分类ID", example = "0")
    private Long parentId;

    @Schema(description = "排序，数字越小越靠前", example = "0")
    private Integer sort;

    @Schema(description = "状态，0-禁用，1-启用", example = "1")
    private Integer status;

    @Schema(description = "分类描述")
    private String description;

    @Schema(description = "分类层级", example = "1")
    private Integer level;

    @Schema(description = "分类路径", example = "1,2,3")
    private String path;

    @Schema(description = "可视范围机构ID")
    private Long visibleOrgId;

    @Schema(description = "可视范围机构名称")
    private String visibleOrgName;
} 