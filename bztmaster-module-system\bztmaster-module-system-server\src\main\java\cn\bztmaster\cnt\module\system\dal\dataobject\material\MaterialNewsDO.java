package cn.bztmaster.cnt.module.system.dal.dataobject.material;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import java.time.LocalDateTime;

/**
 * 图文素材 DO
 */
@TableName("mp_material_news")
@KeySequence("mp_material_news_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialNewsDO extends BaseDO {
    /** 主键，自增 */
    @TableId
    private Long id;
    /** 图文名称 */
    private String name;
    /** 分类ID，关联mp_material_category.id */
    private Long categoryId;
    /** 微信媒体ID */
    private String mediaId;
    /** 图文内容（JSON格式，包含多个文章） */
    private String content;
    /** 缩略图URL */
    @TableField("thumb_url")
    private String thumbUrl;
    /** 文章数量 */
    private Integer articleCount;
    /** 素材来源类型，1-本地上传，2-微信同步，3-外部链接 */
    private Integer sourceType;
    /** 来源机构ID */
    private Long sourceOrgId;
    /** 来源机构名称 */
    private String sourceOrgName;
    /** 公众号账号ID */
    private Long accountId;
    /** 状态，0-草稿，1-已发布，2-已下线 */
    private Integer status;
    /** 发布时间 */
    private LocalDateTime publishTime;
    /** 标签，多个标签用逗号分隔 */
    private String tags;
    /** 图文描述 */
    private String description;
    /** 是否永久素材，0-临时，1-永久 */
    private Boolean isPermanent;
    /** 过期时间（临时素材） */
    private LocalDateTime expireTime;
    /** 图文类型，1-已发布，2-草稿 */
    private Integer newsType;
    /** 可视范围机构ID */
    private Long visibleOrgId;
    /** 可视范围机构名称 */
    private String visibleOrgName;
    /** 租户ID */
    private Long tenantId;
} 