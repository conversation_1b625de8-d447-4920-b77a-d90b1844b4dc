package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@TableName("publicbiz_package_feature")
@Schema(description = "服务套餐特色标签表 DO")
public class ServicePackageFeatureDO {
    @TableId
    private Long id;
    private Long packageId;
    private String featureName;
    private Integer sortOrder;
    private Date createTime;
    private Date updateTime;
    private String creator;
    private String updater;
    private Boolean deleted;
    private Long tenantId;
} 