package cn.bztmaster.cnt.module.publicbiz.controller.admin.leads.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 线索信息分页查询 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 线索信息分页查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LeadsPageReqVO extends PageParam {

    @Schema(description = "主键", example = "1")
    private Long id;

    @Schema(description = "线索id", example = "LEAD20250722704")
    private String leadId;

    @Schema(description = "关键词搜索（客户姓名、客户手机号）", example = "张三")
    private String keyword;

    @Schema(description = "客户姓名，模糊匹配", example = "张")
    private String customerName;

    @Schema(description = "联系电话，模糊匹配", example = "138")
    private String customerPhone;

    @Schema(description = "线索来源", example = "1")
    private Integer leadSource;

    @Schema(description = "业务模块", example = "1")
    private Integer businessModule;

    @Schema(description = "线索状态", example = "1")
    private Integer leadStatus;

    @Schema(description = "创建方式", example = "1")
    private Integer createMethod;

    @Schema(description = "当前跟进人", example = "李四")
    private String currentOwner;

    @Schema(description = "创建时间范围开始", example = "2025-07-01 00:00:00")
    private String beginCreateTime;

    @Schema(description = "创建时间范围结束", example = "2025-07-21 23:59:59")
    private String endCreateTime;
}