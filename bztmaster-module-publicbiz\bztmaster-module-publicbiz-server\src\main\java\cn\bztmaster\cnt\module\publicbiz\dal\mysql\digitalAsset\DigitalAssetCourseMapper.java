package cn.bztmaster.cnt.module.publicbiz.dal.mysql.digitalAsset;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo.DigitalAssetCoursePageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo.CourseStatisticsRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset.DigitalAssetCourseDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 数字资产课程 Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface DigitalAssetCourseMapper extends BaseMapperX<DigitalAssetCourseDO> {

    /**
     * 分页查询课程列表
     */
    default PageResult<DigitalAssetCourseDO> selectPage(DigitalAssetCoursePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DigitalAssetCourseDO>()
                .likeIfPresent(DigitalAssetCourseDO::getName, reqVO.getKeyword())
                .eqIfPresent(DigitalAssetCourseDO::getCategory, reqVO.getCategory())
                .eqIfPresent(DigitalAssetCourseDO::getStatus, reqVO.getStatus())
                .eqIfPresent(DigitalAssetCourseDO::getTeachType, reqVO.getTeachType())
                .eqIfPresent(DigitalAssetCourseDO::getBusinessModule, reqVO.getBusinessModule())
                .eqIfPresent(DigitalAssetCourseDO::getMerchant, reqVO.getMerchant())
                .orderByDesc(DigitalAssetCourseDO::getId));
    }

    /**
     * 获取课程统计概览
     */
    CourseStatisticsRespVO selectStatistics();

    /**
     * 根据课程名称查询（用于重复性校验）
     */
    DigitalAssetCourseDO selectByName(@Param("name") String name, @Param("excludeId") Long excludeId);

    /**
     * 根据业务板块统计课程数量
     */
    Integer countByBusinessModule(@Param("businessModule") String businessModule);

    /**
     * 根据讲师ID查询课程列表
     */
    List<DigitalAssetCourseDO> selectByTeacherId(@Param("teacherId") Long teacherId);
}
