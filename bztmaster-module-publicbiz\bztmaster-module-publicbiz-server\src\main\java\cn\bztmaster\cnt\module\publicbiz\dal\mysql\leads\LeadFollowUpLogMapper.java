package cn.bztmaster.cnt.module.publicbiz.dal.mysql.leads;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.leads.vo.LeadFollowUpLogPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadFollowUpLogDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 线索跟进记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LeadFollowUpLogMapper extends BaseMapperX<LeadFollowUpLogDO> {
    
    /**
     * 分页查询线索跟进记录
     */
    PageResult<LeadFollowUpLogDO> selectPage(@Param("reqVO") LeadFollowUpLogPageReqVO reqVO);
    
    /**
     * 根据线索ID查询跟进记录列表
     */
    List<LeadFollowUpLogDO> selectListByLeadId(@Param("leadId") String leadId);
    
    /**
     * 根据线索ID列表查询跟进记录
     */
    List<LeadFollowUpLogDO> selectListByLeadIds(@Param("leadIds") List<String> leadIds);
    
    /**
     * 根据线索ID删除跟进记录
     */
    int deleteByLeadId(@Param("leadId") String leadId);
}