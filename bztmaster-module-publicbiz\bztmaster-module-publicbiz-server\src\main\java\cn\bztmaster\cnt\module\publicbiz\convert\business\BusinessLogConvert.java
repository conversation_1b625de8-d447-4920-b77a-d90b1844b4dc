package cn.bztmaster.cnt.module.publicbiz.convert.business;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.business.BusinessLogDO;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper(componentModel = "spring")
public interface BusinessLogConvert {
    BusinessLogConvert INSTANCE = Mappers.getMapper(BusinessLogConvert.class);

    BusinessLogDO convert(BusinessLogSaveReqVO bean);
    BusinessLogRespVO convert(BusinessLogDO bean);
    List<BusinessLogRespVO> convertList(List<BusinessLogDO> list);
    PageResult<BusinessLogRespVO> convertPage(PageResult<BusinessLogDO> page);
} 