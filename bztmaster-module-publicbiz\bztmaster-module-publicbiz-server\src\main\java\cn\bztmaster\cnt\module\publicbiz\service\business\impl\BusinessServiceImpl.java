package cn.bztmaster.cnt.module.publicbiz.service.business.impl;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.business.BusinessDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.business.BusinessFollowupDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.business.BusinessMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.business.BusinessFollowupMapper;
import cn.bztmaster.cnt.module.publicbiz.service.business.BusinessService;
import cn.bztmaster.cnt.module.publicbiz.service.business.BusinessLogService;
import cn.bztmaster.cnt.module.publicbiz.convert.business.BusinessConvert;
import cn.bztmaster.cnt.module.publicbiz.convert.business.BusinessFollowupConvert;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Collection;
import java.util.Collections;

@Service
public class BusinessServiceImpl implements BusinessService {
    @Resource
    private BusinessMapper businessMapper;
    @Resource
    private BusinessFollowupMapper businessFollowupMapper;
    @Resource
    private BusinessLogService businessLogService;
    @Resource
    private BusinessConvert businessConvert;
    @Resource
    private BusinessFollowupConvert businessFollowupConvert;

    @Override
    public PageResult<BusinessRespVO> getBusinessPage(BusinessPageReqVO reqVO) {
        PageResult<BusinessDO> pageResult = businessMapper.selectPage(reqVO);
        return businessConvert.convertPage(pageResult);
    }

    @Override
    @Transactional
    public Long createBusiness(BusinessSaveReqVO reqVO) {
        BusinessDO business = businessConvert.convert(reqVO);
        business.setCreateTime(new Date());
        business.setDeleted(false);
        businessMapper.insert(business);
        // 日志
        BusinessLogSaveReqVO logReqVO = new BusinessLogSaveReqVO();
        logReqVO.setBusinessId(business.getId());
        logReqVO.setAction("创建");
        logReqVO.setContent("创建商机：" + business.getName());
        logReqVO.setActionTime(new Date());
        logReqVO.setActionUserId(business.getOwnerUserId());
        logReqVO.setActionUserName(business.getOwnerUserName());
        logReqVO.setCreator(business.getCreator());
        logReqVO.setDeleted(false);
        businessLogService.createBusinessLog(logReqVO);
        return business.getId();
    }

    @Override
    @Transactional
    public void updateBusiness(BusinessSaveReqVO reqVO) {
        BusinessDO business = businessConvert.convert(reqVO);
        business.setUpdateTime(new Date());
        businessMapper.updateById(business);
        // 日志
        BusinessLogSaveReqVO logReqVO = new BusinessLogSaveReqVO();
        logReqVO.setBusinessId(business.getId());
        logReqVO.setAction("修改");
        logReqVO.setContent("修改商机：" + business.getName());
        logReqVO.setActionTime(new Date());
        logReqVO.setActionUserId(business.getOwnerUserId());
        logReqVO.setActionUserName(business.getOwnerUserName());
        logReqVO.setUpdater(business.getUpdater());
        logReqVO.setDeleted(false);
        businessLogService.createBusinessLog(logReqVO);
    }

    @Override
    @Transactional
    public void deleteBusiness(Long id) {
        BusinessDO business = businessMapper.selectById(id);
        if (business != null) {
            business.setDeleted(true);
            business.setUpdateTime(new Date());
            businessMapper.updateById(business);
            // 日志
            BusinessLogSaveReqVO logReqVO = new BusinessLogSaveReqVO();
            logReqVO.setBusinessId(id);
            logReqVO.setAction("删除");
            logReqVO.setContent("删除商机：" + business.getName());
            logReqVO.setActionTime(new Date());
            logReqVO.setActionUserId(business.getOwnerUserId());
            logReqVO.setActionUserName(business.getOwnerUserName());
            logReqVO.setUpdater(business.getUpdater());
            logReqVO.setDeleted(true);
            businessLogService.createBusinessLog(logReqVO);
        }
    }

    @Override
    public BusinessDetailRespVO getBusinessDetail(Long id) {
        BusinessDO business = businessMapper.selectById(id);
        if (business == null || Boolean.TRUE.equals(business.getDeleted())) {
            return null;
        }
        BusinessRespVO businessRespVO = businessConvert.convert(business);
        List<BusinessFollowupDO> followups = businessFollowupMapper.selectListByBusinessId(id);
        List<BusinessFollowupRespVO> followupRespVOs = businessFollowupConvert.convertList(followups);
        BusinessDetailRespVO detailRespVO = new BusinessDetailRespVO();
        detailRespVO.setBusiness(businessRespVO);
        detailRespVO.setFollowups(followupRespVOs);
        return detailRespVO;
    }

    @Override
    public BusinessDO getBusiness(Long id) {
        return businessMapper.selectById(id);
    }

    @Override
    public List<BusinessDO> getBusinessList(Collection<Long> ids) {
        return (ids == null || ids.isEmpty()) ? Collections.emptyList() : businessMapper.selectBatchIds(ids);
    }
} 