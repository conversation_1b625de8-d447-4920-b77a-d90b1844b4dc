<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.leads.LeadFollowUpLogMapper">
    <select id="selectPage" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadFollowUpLogDO">
        SELECT * FROM publicbiz_lead_follow_up_log
        WHERE deleted = 0
        <if test="reqVO.id != null">
            AND id = #{reqVO.id}
        </if>
        <if test="reqVO.leadId != null and reqVO.leadId != ''">
            AND lead_id = #{reqVO.leadId}
        </if>
        <if test="reqVO.followUpType != null">
            AND follow_up_type = #{reqVO.followUpType}
        </if>
        <if test="reqVO.followUpContent != null and reqVO.followUpContent != ''">
            AND follow_up_content LIKE CONCAT('%', #{reqVO.followUpContent}, '%')
        </if>
        <if test="reqVO.followUpUserId != null">
            AND follow_up_user_id = #{reqVO.followUpUserId}
        </if>
        <if test="reqVO.followUpUserName != null and reqVO.followUpUserName != ''">
            AND follow_up_user_name LIKE CONCAT('%', #{reqVO.followUpUserName}, '%')
        </if>
        <if test="reqVO.beginCreateTime != null and reqVO.beginCreateTime != ''">
            AND create_time >= #{reqVO.beginCreateTime}
        </if>
        <if test="reqVO.endCreateTime != null and reqVO.endCreateTime != ''">
            AND create_time &lt;= #{reqVO.endCreateTime}
        </if>
        ORDER BY create_time DESC
    </select>
    
    <select id="selectListByLeadId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadFollowUpLogDO">
        SELECT * FROM publicbiz_lead_follow_up_log
        WHERE deleted = 0
        AND lead_id = #{leadId}
        ORDER BY create_time DESC
    </select>
    
    <select id="selectListByLeadIds" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadFollowUpLogDO">
        SELECT * FROM publicbiz_lead_follow_up_log
        WHERE deleted = 0
        AND lead_id IN
        <foreach collection="leadIds" item="leadId" open="(" separator="," close=")">
            #{leadId}
        </foreach>
        ORDER BY create_time DESC
    </select>
    
    <delete id="deleteByLeadId">
        UPDATE publicbiz_lead_follow_up_log
        SET deleted = 1
        WHERE lead_id = #{leadId}
    </delete>
</mapper>