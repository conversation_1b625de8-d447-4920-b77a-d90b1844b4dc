package cn.bztmaster.cnt.module.system.service.material;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialVideoDO;

import java.util.Collection;
import java.util.List;

public interface MaterialVideoService {
    PageResult<VideoRespVO> getVideoPage(VideoPageReqVO reqVO);
    Long createVideo(VideoSaveReqVO reqVO);
    void updateVideo(VideoSaveReqVO reqVO);
    void deleteVideo(Long id);
    VideoRespVO getVideoDetail(Long id);
    
    // API接口需要的方法
    MaterialVideoDO getVideo(Long id);
    List<MaterialVideoDO> getVideoList(Collection<Long> ids);
    List<MaterialVideoDO> getVideoListByCategoryId(Long categoryId);
    List<MaterialVideoDO> getVideoListBySourceOrgId(Long sourceOrgId);
    void validateVideoList(Collection<Long> ids);
    PageResult<VideoRespVO> getVideoRecyclePage(VideoPageReqVO reqVO, String updateTimeFrom, String updateTimeTo);
    void restoreVideoFromRecycle(java.util.List<Long> idList);
    void deleteVideoFromRecycle(java.util.List<Long> idList);
} 