package cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "商机中心 - 商机跟进分页 Request VO")
public class BusinessFollowupPageReqVO extends PageParam {
    private Long id;
    private Long tenantId;
    private Long businessId;
    private String content;
    private Date followTime;
    private Long followUserId;
    private String followUserName;
    private String creator;
    private String updater;
    private Boolean deleted;
} 