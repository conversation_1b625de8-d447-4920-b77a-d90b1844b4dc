package cn.bztmaster.cnt.module.talentpool.api.talent;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserPageReqDTO;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserRespDTO;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserUpdateReqDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 人才用户 API 接口
 *
 * 提供人才库用户的分页、详情、编辑、停用、合并等操作。
 * 业务逻辑严格遵循多级识别（身份证号、手机号、UnionID、人工辅助）合并规则。
 *
 * <AUTHOR>
 */
@FeignClient(name = "talentpool-service")
@Tag(name = "RPC 服务 - 人才用户")
public interface TalentUserApi {

    String PREFIX = "/apachecore/talentpool";

    /**
     * 分页查询人才库用户列表
     * @param reqDTO 分页请求参数
     * @return 分页结果
     */
    @GetMapping(PREFIX + "/page")
    @Operation(summary = "分页查询人才库用户列表")
    CommonResult<List<TalentUserRespDTO>> getTalentUserPage(@Valid TalentUserPageReqDTO reqDTO);

    /**
     * 根据ID获取人才详细信息
     * @param id 用户ID
     * @return 详细信息
     */
    @GetMapping(PREFIX + "/get")
    @Operation(summary = "根据ID获取人才详细信息")
    CommonResult<TalentUserRespDTO> getTalentUser(@RequestParam("id") Long id);

    /**
     * 编辑人才基本信息
     * @param reqDTO 编辑请求参数
     * @return 是否成功
     */
    @PutMapping(PREFIX + "/update")
    @Operation(summary = "编辑人才基本信息")
    CommonResult<Boolean> updateTalentUser(@Valid @RequestBody TalentUserUpdateReqDTO reqDTO);

    /**
     * 停用人才，将状态设为已禁用
     * @param id 用户ID
     * @return 是否成功
     */
    @PostMapping(PREFIX + "/disable")
    @Operation(summary = "停用人才")
    CommonResult<Boolean> disableTalentUser(@RequestParam("id") Long id);

    /**
     * 合并人才，严格按照多级识别规则
     * @param id 待合并用户ID
     * @return 是否成功
     */
    @PostMapping(PREFIX + "/merge")
    @Operation(summary = "合并人才（多级识别：身份证号/手机号/UnionID/人工辅助）")
    CommonResult<Boolean> mergeTalentUser(@RequestParam("id") Long id);
} 