package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads;

import cn.bztmaster.cnt.framework.tenant.core.db.TenantBaseDO;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.BusinessModuleEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.CreateMethodEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.LeadSourceEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.LeadStatusEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 线索信息 DO
 * 
 * 对应表：lead_info
 *
 * <AUTHOR>
 */
@TableName("publicbiz_lead_info")
@KeySequence("lead_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LeadInfoDO extends TenantBaseDO {

    /**
     * 主键，自增
     */
    @TableId
    private Long id;

    /**
     * 线索ID，系统生成的唯一标识
     */
    @NotEmpty(message = "线索ID不能为空")
    @Size(max = 32, message = "线索ID长度不能超过32")
    private String leadId;

    /**
     * 客户姓名
     */
    @NotEmpty(message = "客户姓名不能为空")
    @Size(max = 100, message = "客户姓名长度不能超过100")
    private String customerName;

    /**
     * 联系电话，11位手机号
     */
    @NotEmpty(message = "联系电话不能为空")
    @Size(max = 20, message = "联系电话长度不能超过20")
    private String customerPhone;

    /**
     * 线索来源
     * 
     * 枚举 {@link LeadSourceEnum}
     */
    @NotNull(message = "线索来源不能为空")
    private Integer leadSource;

    /**
     * 业务模块
     * 
     * 枚举 {@link BusinessModuleEnum}
     */
    @NotNull(message = "业务模块不能为空")
    private Integer businessModule;

    /**
     * 线索状态
     * 
     * 枚举 {@link LeadStatusEnum}
     */
    @NotNull(message = "线索状态不能为空")
    private Integer leadStatus;

    /**
     * 创建方式
     * 
     * 枚举 {@link CreateMethodEnum}
     */
    @NotNull(message = "创建方式不能为空")
    private Integer createMethod;

    /**
     * 当前跟进人ID
     */
    @Size(max = 64, message = "当前跟进人ID长度不能超过64")
    private String currentOwner;

    /**
     * 当前跟进人姓名
     */
    @Size(max = 64, message = "当前跟进人姓名长度不能超过64")
    private String currentOwnerName;

    /**
     * 创建人姓名
     */
    @Size(max = 64, message = "创建人姓名长度不能超过64")
    private String creatorName;

    /**
     * 备注信息
     */
    private String remark;

}