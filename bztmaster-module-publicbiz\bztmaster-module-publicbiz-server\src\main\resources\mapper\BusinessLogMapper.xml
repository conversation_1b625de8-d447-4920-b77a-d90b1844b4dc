<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.business.BusinessLogMapper">
    <select id="selectPage" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.business.BusinessLogDO">
        SELECT * FROM publicbiz_business_log
        WHERE deleted = 0
        <if test="reqVO.id != null">
            AND id = #{reqVO.id}
        </if>
        <if test="reqVO.tenantId != null">
            AND tenant_id = #{reqVO.tenantId}
        </if>
        <if test="reqVO.businessId != null">
            AND business_id = #{reqVO.businessId}
        </if>
        <if test="reqVO.action != null and reqVO.action != ''">
            AND action = #{reqVO.action}
        </if>
        <if test="reqVO.content != null and reqVO.content != ''">
            AND content LIKE CONCAT('%', #{reqVO.content}, '%')
        </if>
        <if test="reqVO.actionUserId != null">
            AND action_user_id = #{reqVO.actionUserId}
        </if>
        <if test="reqVO.actionUserName != null and reqVO.actionUserName != ''">
            AND action_user_name LIKE CONCAT('%', #{reqVO.actionUserName}, '%')
        </if>
        <if test="reqVO.creator != null and reqVO.creator != ''">
            AND creator = #{reqVO.creator}
        </if>
        <if test="reqVO.updater != null and reqVO.updater != ''">
            AND updater = #{reqVO.updater}
        </if>
        <if test="reqVO.deleted != null">
            AND deleted = #{reqVO.deleted}
        </if>
        ORDER BY id DESC
    </select>
</mapper>