package cn.bztmaster.cnt.module.system.service.material.impl;

import cn.bztmaster.cnt.module.system.controller.admin.material.vo.*;
import cn.bztmaster.cnt.module.system.dal.dataobject.material.MaterialVideoDO;
import cn.bztmaster.cnt.module.system.dal.mysql.material.MaterialVideoMapper;
import cn.bztmaster.cnt.module.system.service.material.MaterialVideoService;
import cn.bztmaster.cnt.module.system.convert.material.MaterialVideoConvert;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

@Service
public class MaterialVideoServiceImpl implements MaterialVideoService {
    @Resource
    private MaterialVideoMapper videoMapper;
    @Resource
    private MaterialVideoConvert videoConvert;

    @Override
    public PageResult<VideoRespVO> getVideoPage(VideoPageReqVO reqVO) {
        PageResult<MaterialVideoDO> pageResult = videoMapper.selectPage(reqVO);
        return videoConvert.convertPage(pageResult);
    }

    @Override
    @Transactional
    public Long createVideo(VideoSaveReqVO reqVO) {
        MaterialVideoDO video = videoConvert.convert(reqVO);
        videoMapper.insert(video);
        return video.getId();
    }

    @Override
    @Transactional
    public void updateVideo(VideoSaveReqVO reqVO) {
        MaterialVideoDO video = videoConvert.convert(reqVO);
        videoMapper.updateById(video);
    }

    @Override
    @Transactional
    public void deleteVideo(Long id) {
        videoMapper.logicDeleteById(id);
    }

    @Override
    public VideoRespVO getVideoDetail(Long id) {
        MaterialVideoDO video = videoMapper.selectById(id);
        if (video == null || Boolean.TRUE.equals(video.getDeleted())) return null;
        return videoConvert.convert(video);
    }

    @Override
    public MaterialVideoDO getVideo(Long id) {
        return videoMapper.selectById(id);
    }

    @Override
    public List<MaterialVideoDO> getVideoList(Collection<Long> ids) {
        return videoMapper.selectBatchIds(ids);
    }

    @Override
    public List<MaterialVideoDO> getVideoListByCategoryId(Long categoryId) {
        return videoMapper.selectListByCategoryId(categoryId);
    }

    @Override
    public List<MaterialVideoDO> getVideoListBySourceOrgId(Long sourceOrgId) {
        return videoMapper.selectListBySourceOrgId(sourceOrgId);
    }

    @Override
    public void validateVideoList(Collection<Long> ids) {
        List<MaterialVideoDO> videos = videoMapper.selectBatchIds(ids);
        if (videos.size() != ids.size()) {
            throw new RuntimeException("存在无效的视频ID");
        }
        // 可以添加其他验证逻辑，比如检查状态等
    }

    @Override
    public PageResult<VideoRespVO> getVideoRecyclePage(VideoPageReqVO reqVO, String updateTimeFrom, String updateTimeTo) {
        PageResult<MaterialVideoDO> pageResult = videoMapper.selectRecyclePage(reqVO, updateTimeFrom, updateTimeTo);
        return videoConvert.convertPage(pageResult);
    }

    @Override
    @Transactional
    public void restoreVideoFromRecycle(java.util.List<Long> idList) {
        if (idList == null || idList.isEmpty()) return;
        videoMapper.restoreFromRecycle(idList);
    }

    @Override
    @Transactional
    public void deleteVideoFromRecycle(java.util.List<Long> idList) {
        if (idList == null || idList.isEmpty()) return;
        videoMapper.deleteFromRecycle(idList);
    }
} 