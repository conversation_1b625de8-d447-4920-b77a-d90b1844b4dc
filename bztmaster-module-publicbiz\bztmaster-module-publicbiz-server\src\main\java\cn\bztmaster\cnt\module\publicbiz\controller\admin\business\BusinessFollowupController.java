package cn.bztmaster.cnt.module.publicbiz.controller.admin.business;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.business.BusinessFollowupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/publicbiz/business/followup")
@Tag(name = "商机中心-商机跟进管理")
public class BusinessFollowupController {

    @Resource
    private BusinessFollowupService businessFollowupService;

    @GetMapping("/list")
    @Operation(summary = "商机跟进分页列表")
    public CommonResult<PageResult<BusinessFollowupRespVO>> list(BusinessFollowupPageReqVO reqVO) {
        return CommonResult.success(businessFollowupService.getBusinessFollowupPage(reqVO));
    }

    @PostMapping("/create")
    @Operation(summary = "新增商机跟进")
    public CommonResult<Long> create(@RequestBody BusinessFollowupSaveReqVO reqVO) {
        return CommonResult.success(businessFollowupService.createBusinessFollowup(reqVO));
    }

    @GetMapping("/detail")
    @Operation(summary = "商机跟进详情")
    public CommonResult<BusinessFollowupRespVO> detail(@RequestParam("id") Long id) {
        return CommonResult.success(businessFollowupService.getBusinessFollowupDetail(id));
    }
} 