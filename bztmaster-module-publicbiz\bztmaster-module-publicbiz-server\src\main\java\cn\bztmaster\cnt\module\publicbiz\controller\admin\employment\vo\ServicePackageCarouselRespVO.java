package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@Schema(description = "就业服务-服务套餐轮播图 Response VO")
public class ServicePackageCarouselRespVO {
    @Schema(description = "轮播图ID")
    private Long id;

    @Schema(description = "套餐ID")
    private Long packageId;

    @Schema(description = "轮播图URL")
    private String imageUrl;

    @Schema(description = "排序")
    private Integer sortOrder;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
} 