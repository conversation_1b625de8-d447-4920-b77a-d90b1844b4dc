package cn.bztmaster.cnt.module.publicbiz.api.business.dto;

import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@Schema(description = "商机中心 - 商机日志 Response DTO")
public class BusinessLogRespDTO implements VO {
    @Schema(description = "日志ID", example = "1")
    private Long id;

    @Schema(description = "商机ID", example = "1001")
    private Long businessId;

    @Schema(description = "操作类型", example = "修改")
    private String operationType;

    @Schema(description = "操作内容", example = "修改商机状态")
    private String content;

    @Schema(description = "操作人ID")
    private Long operatorId;

    @Schema(description = "操作人名称")
    private String operatorName;

    @Schema(description = "操作时间")
    private Date operateTime;
} 