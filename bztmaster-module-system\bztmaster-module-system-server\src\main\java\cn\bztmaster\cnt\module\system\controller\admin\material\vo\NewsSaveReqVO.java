package cn.bztmaster.cnt.module.system.controller.admin.material.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@Schema(description = "管理后台 - 图文素材新增/更新 Request VO")
public class NewsSaveReqVO {
    @Schema(description = "图文ID", example = "1")
    private Long id;

    @Schema(description = "图文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "图文名称不能为空")
    private String name;

    @Schema(description = "分类ID，关联mp_material_category.id")
    private Long categoryId;

    @Schema(description = "微信媒体ID")
    private String mediaId;

    @Schema(description = "图文内容，文案内容")
    private String content;

    @Schema(description = "缩略图URL")
    private String thumbUrl;

    @Schema(description = "文章数量")
    private Integer articleCount;

    @Schema(description = "素材来源类型，1-本地上传，2-微信同步，3-外部链接")
    private Integer sourceType;

    @Schema(description = "来源机构ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "来源机构ID不能为空")
    private Long sourceOrgId;

    @Schema(description = "来源机构名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "来源机构名称不能为空")
    private String sourceOrgName;

    @Schema(description = "公众号账号ID")
    private Long accountId;

    @Schema(description = "状态，0-草稿，1-已发布，2-已下线")
    private Integer status;

    @Schema(description = "发布时间")
    private LocalDateTime publishTime;

    @Schema(description = "标签，多个标签用逗号分隔")
    private String tags;

    @Schema(description = "图文描述")
    private String description;

    @Schema(description = "是否永久素材，0-临时，1-永久")
    private Boolean isPermanent;

    @Schema(description = "过期时间（临时素材）")
    private LocalDateTime expireTime;

    @Schema(description = "图文类型，1-已发布，2-草稿")
    private Integer newsType;

    @Schema(description = "可视范围机构ID")
    private Long visibleOrgId;

    @Schema(description = "可视范围机构名称")
    private String visibleOrgName;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除，0-未删除，1-已删除")
    private Boolean deleted;
} 