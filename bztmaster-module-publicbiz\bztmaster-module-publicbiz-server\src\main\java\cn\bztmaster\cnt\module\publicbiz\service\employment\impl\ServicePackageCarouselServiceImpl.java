package cn.bztmaster.cnt.module.publicbiz.service.employment.impl;

import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.ServicePackageCarouselSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.convert.employment.ServicePackageCarouselConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageCarouselDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.ServicePackageCarouselMapper;
import cn.bztmaster.cnt.module.publicbiz.service.employment.ServicePackageCarouselService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;

import static cn.bztmaster.cnt.module.publicbiz.enums.LogRecordConstants.*;

@Service
@Validated
@Slf4j
public class ServicePackageCarouselServiceImpl implements ServicePackageCarouselService {

    @Resource
    private ServicePackageCarouselMapper servicePackageCarouselMapper;

    @Override
    @Transactional
    @LogRecord(type = SERVICE_PACKAGE_CAROUSEL_TYPE, subType = SERVICE_PACKAGE_CAROUSEL_CREATE_SUB_TYPE, bizNo = "{{#result}}", success = SERVICE_PACKAGE_CAROUSEL_CREATE_SUCCESS)
    public Long createServicePackageCarousel(ServicePackageCarouselSaveReqVO createReqVO) {
        ServicePackageCarouselDO carousel = ServicePackageCarouselConvert.INSTANCE.convert(createReqVO);
        carousel.setCreateTime(new Date());
        carousel.setDeleted(false);
        carousel.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
        carousel.setTenantId(SecurityFrameworkUtils.getLoginUser() != null ? SecurityFrameworkUtils.getLoginUser().getTenantId() : 1L);
        servicePackageCarouselMapper.insert(carousel);

        // 记录操作日志上下文
        LogRecordContext.putVariable("carousel", carousel);
        return carousel.getId();
    }

    @Override
    @Transactional
    @LogRecord(type = SERVICE_PACKAGE_CAROUSEL_TYPE, subType = SERVICE_PACKAGE_CAROUSEL_DELETE_SUB_TYPE, bizNo = "{{#id}}", success = SERVICE_PACKAGE_CAROUSEL_DELETE_SUCCESS)
    public void deleteServicePackageCarousel(Long id) {
        ServicePackageCarouselDO carousel = servicePackageCarouselMapper.selectById(id);
        if (carousel != null) {
            servicePackageCarouselMapper.deleteById(id);
            // 记录操作日志上下文
            LogRecordContext.putVariable("carousel", carousel);
        }
    }
} 